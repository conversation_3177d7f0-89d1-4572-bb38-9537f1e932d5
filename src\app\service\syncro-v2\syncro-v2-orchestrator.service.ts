import { Injectable, inject } from '@angular/core';
import { SyncroV2Service } from './syncro-v2.service';
import { SyncroV2DbService } from './syncro-v2-db.service';
import { BasicResponse } from '../data/basic-response';
import { Category } from '../data/category';

export interface SyncProgress {
  currentPage: number;
  totalPages?: number;
  totalCount?: number;
  categoriesProcessed: number;
  productsProcessed?: number;
  deletedCategoriesProcessed: number;
  isComplete: boolean;
  error?: string;
}

export interface SyncResult {
  success: boolean;
  totalCategoriesProcessed: number;
  totalProductsProcessed?: number;
  totalDeletedCategoriesProcessed: number;
  totalPages: number;
  duration: number;
  error?: string;
}

@Injectable({
  providedIn: 'root'
})
export class SyncroV2OrchestratorService {

  private _syncroV2Service = inject(SyncroV2Service);
  private _syncroV2DbService = inject(SyncroV2DbService);

  constructor() { }

  /**
   * Inizializza il database per la sincronizzazione v2
   */
  async initializeDatabase(): Promise<void> {
    await this._syncroV2DbService.updateDatabaseSchema();
  }

  /**
   * Ottiene le root categories identificate tramite regex durante la sincronizzazione v2
   * @param idCatalog ID del catalogo (opzionale)
   * @returns Promise<Category[]> Array delle root categories
   */
  async getRootCategoriesByRegex(idCatalog?: number): Promise<Category[]> {
    try {
      return await this._syncroV2DbService.getRootCategoriesByRegex(idCatalog);
    } catch (error) {
      console.error('❌ Errore nel recupero delle root categories tramite regex:', error);
      return [];
    }
  }

  /**
   * Esegue una sincronizzazione completa delle categorie per un catalogo
   * @param idCatalog ID del catalogo
   * @param pageSize Dimensione della pagina (default: 10)
   * @param onProgress Callback per il progresso
   * @returns Promise<SyncResult>
   */
  async syncCatalogCategories(
    idCatalog: number,
    pageSize: number = 10,
    onProgress?: (progress: SyncProgress) => void
  ): Promise<SyncResult> {
    const startTime = Date.now();
    let totalCategoriesProcessed = 0;
    let totalPages = 0;

    try {
      // Segna l'inizio della sincronizzazione
      await this._syncroV2DbService.markSyncStart(idCatalog, startTime);

      // Sincronizza tutte le categorie
      await this._syncroV2Service.syncAllCategories(
        idCatalog,
        pageSize,
        async (pageData: BasicResponse, pageNumber: number, totalPagesFromAPI?: number, totalCountFromAPI?: number) => {
          if (pageData.status === 'OK' && pageData.content) {
            // Validazione del contenuto e estrazione delle categorie
            let categories: Category[] = [];

            // Gestisce la nuova struttura con content.results
            if (pageData.content.results && Array.isArray(pageData.content.results)) {
              categories = pageData.content.results;
            } else if (Array.isArray(pageData.content)) {
              // Fallback per la struttura precedente
              categories = pageData.content;
            } else {
              console.warn(`⚠️ Pagina ${pageNumber}: content non ha una struttura valida`, pageData.content);
              return;
            }

            // Aggiorna totalPages se disponibile dall'API
            if (totalPagesFromAPI !== undefined) {
              totalPages = totalPagesFromAPI;
            } else {
              totalPages = pageNumber + 1;
            }

            console.log(`📦 Pagina ${pageNumber + 1}/${totalPages}: ricevute ${categories.length} categorie`);
            if (totalCountFromAPI !== undefined) {
              console.log(`📊 Progresso: ${totalCategoriesProcessed + categories.length}/${totalCountFromAPI} categorie totali`);
            }

            // Salva le categorie nel database
            await this._syncroV2DbService.saveCategoriesV2(
              categories,
              idCatalog,
              pageNumber,
              startTime
            );

            totalCategoriesProcessed += categories.length;

            // Notifica il progresso
            if (onProgress) {
              onProgress({
                currentPage: pageNumber,
                categoriesProcessed: totalCategoriesProcessed,
                deletedCategoriesProcessed: 0,
                isComplete: false,
                totalPages: totalPages,
                totalCount: totalCountFromAPI
              });
            }
          } else {
            console.warn(`⚠️ Pagina ${pageNumber}: risposta non valida`, pageData);
          }
        }
      );

      // Segna la fine della sincronizzazione
      const endTime = Date.now();
      await this._syncroV2DbService.markSyncComplete(idCatalog, endTime, totalPages);

      return {
        success: true,
        totalCategoriesProcessed,
        totalDeletedCategoriesProcessed: 0,
        totalPages,
        duration: endTime - startTime
      };

    } catch (error) {
      console.error('Errore durante la sincronizzazione delle categorie:', error);
      
      if (onProgress) {
        onProgress({
          currentPage: totalPages,
          categoriesProcessed: totalCategoriesProcessed,
          deletedCategoriesProcessed: 0,
          isComplete: true,
          error: error.message
        });
      }

      return {
        success: false,
        totalCategoriesProcessed,
        totalDeletedCategoriesProcessed: 0,
        totalPages,
        duration: Date.now() - startTime,
        error: error.message
      };
    }
  }

  /**
   * Esegue una sincronizzazione incrementale delle categorie cancellate
   * @param idCatalog ID del catalogo
   * @param fromTimestamp Timestamp di partenza (opzionale)
   * @param pageSize Dimensione della pagina (default: 10)
   * @param onProgress Callback per il progresso
   * @returns Promise<SyncResult>
   */
  async syncDeletedCategories(
    idCatalog: number,
    fromTimestamp?: number,
    pageSize: number = 10,
    onProgress?: (progress: SyncProgress) => void
  ): Promise<SyncResult> {
    const startTime = Date.now();
    let totalDeletedCategoriesProcessed = 0;
    let totalPages = 0;

    try {
      // Se non è fornito un timestamp, usa l'ultimo timestamp di sincronizzazione
      if (!fromTimestamp) {
        fromTimestamp = await this._syncroV2DbService.getLastSyncTimestamp(idCatalog);
      }

      // Sincronizza tutte le categorie cancellate
      await this._syncroV2Service.syncAllDeletedCategories(
        idCatalog,
        fromTimestamp,
        pageSize,
        async (pageData: BasicResponse, pageNumber: number, totalPagesFromAPI?: number, totalCountFromAPI?: number) => {
          if (pageData.status === 'OK' && pageData.content) {
            // Validazione del contenuto e estrazione delle categorie cancellate
            let deletedCategories: Category[] = [];

            // Gestisce la nuova struttura con content.results
            if (pageData.content.results && Array.isArray(pageData.content.results)) {
              deletedCategories = pageData.content.results;
            } else if (Array.isArray(pageData.content)) {
              // Fallback per la struttura precedente
              deletedCategories = pageData.content;
            } else {
              console.warn(`⚠️ Pagina ${pageNumber} (categorie cancellate): content non ha una struttura valida`, pageData.content);
              return;
            }

            // Aggiorna totalPages se disponibile dall'API
            if (totalPagesFromAPI !== undefined) {
              totalPages = totalPagesFromAPI;
            } else {
              totalPages = pageNumber + 1;
            }

            console.log(`🗑️ Pagina ${pageNumber + 1}/${totalPages}: ricevute ${deletedCategories.length} categorie cancellate`);
            if (totalCountFromAPI !== undefined) {
              console.log(`📊 Progresso cancellate: ${totalDeletedCategoriesProcessed + deletedCategories.length}/${totalCountFromAPI} categorie totali`);
            }

            // Gestisci le categorie cancellate
            await this._syncroV2DbService.handleDeletedCategoriesV2(
              deletedCategories,
              idCatalog,
              pageNumber
            );

            totalDeletedCategoriesProcessed += deletedCategories.length;

            // Notifica il progresso
            if (onProgress) {
              onProgress({
                currentPage: pageNumber,
                categoriesProcessed: 0,
                deletedCategoriesProcessed: totalDeletedCategoriesProcessed,
                isComplete: false,
                totalPages: totalPages,
                totalCount: totalCountFromAPI
              });
            }
          } else {
            console.warn(`⚠️ Pagina ${pageNumber} (categorie cancellate): risposta non valida`, pageData);
          }
        }
      );

      return {
        success: true,
        totalCategoriesProcessed: 0,
        totalDeletedCategoriesProcessed,
        totalPages,
        duration: Date.now() - startTime
      };

    } catch (error) {
      console.error('Errore durante la sincronizzazione delle categorie cancellate:', error);
      
      if (onProgress) {
        onProgress({
          currentPage: totalPages,
          categoriesProcessed: 0,
          deletedCategoriesProcessed: totalDeletedCategoriesProcessed,
          isComplete: true,
          error: error.message
        });
      }

      return {
        success: false,
        totalCategoriesProcessed: 0,
        totalDeletedCategoriesProcessed,
        totalPages,
        duration: Date.now() - startTime,
        error: error.message
      };
    }
  }

  /**
   * Esegue una sincronizzazione completa (categorie + categorie cancellate)
   * @param idCatalog ID del catalogo
   * @param pageSize Dimensione della pagina (default: 10)
   * @param onProgress Callback per il progresso
   * @returns Promise<SyncResult>
   */
  async syncComplete(
    idCatalog: number,
    pageSize: number = 10,
    onProgress?: (progress: SyncProgress) => void
  ): Promise<SyncResult> {
    const startTime = Date.now();

    try {
      // Prima sincronizza le categorie
      const categoriesResult = await this.syncCatalogCategories(
        idCatalog,
        pageSize,
        (progress) => {
          if (onProgress) {
            onProgress({
              ...progress,
              isComplete: false
            });
          }
        }
      );

      if (!categoriesResult.success) {
        return categoriesResult;
      }

      // Poi sincronizza le categorie cancellate
      const deletedResult = await this.syncDeletedCategories(
        idCatalog,
        undefined, // Usa l'ultimo timestamp automaticamente
        pageSize,
        (progress) => {
          if (onProgress) {
            onProgress({
              currentPage: progress.currentPage,
              categoriesProcessed: categoriesResult.totalCategoriesProcessed,
              deletedCategoriesProcessed: progress.deletedCategoriesProcessed,
              isComplete: false
            });
          }
        }
      );

      // Combina i risultati
      const combinedResult: SyncResult = {
        success: categoriesResult.success && deletedResult.success,
        totalCategoriesProcessed: categoriesResult.totalCategoriesProcessed,
        totalDeletedCategoriesProcessed: deletedResult.totalDeletedCategoriesProcessed,
        totalPages: categoriesResult.totalPages + deletedResult.totalPages,
        duration: Date.now() - startTime,
        error: deletedResult.error || categoriesResult.error
      };

      // Notifica il completamento
      if (onProgress) {
        onProgress({
          currentPage: combinedResult.totalPages,
          categoriesProcessed: combinedResult.totalCategoriesProcessed,
          deletedCategoriesProcessed: combinedResult.totalDeletedCategoriesProcessed,
          isComplete: true,
          error: combinedResult.error
        });
      }

      return combinedResult;

    } catch (error) {
      console.error('Errore durante la sincronizzazione completa:', error);
      
      return {
        success: false,
        totalCategoriesProcessed: 0,
        totalDeletedCategoriesProcessed: 0,
        totalPages: 0,
        duration: Date.now() - startTime,
        error: error.message
      };
    }
  }

  /**
   * Sincronizza tutti i prodotti di un catalogo con paginazione automatica
   * @param idCatalog ID del catalogo
   * @param pageSize Dimensione della pagina (default: 100)
   * @param idRootCategory ID della categoria radice (opzionale)
   * @param onProgress Callback per il progresso
   * @returns Promise<SyncResult>
   */
  async syncCatalogProducts(
    idCatalog: number,
    pageSize: number = 100,
    idRootCategory?: string,
    onProgress?: (progress: SyncProgress) => void
  ): Promise<SyncResult> {
    const startTime = Date.now();
    let totalProductsProcessed = 0;
    let totalPages = 0;

    try {
      console.log(`🚀 Avvio sincronizzazione prodotti per catalogo ${idCatalog}${idRootCategory ? ` categoria ${idRootCategory}` : ''}`);

      // Segna l'inizio della sincronizzazione
      await this._syncroV2DbService.markSyncStart(idCatalog, startTime);

      // Ottieni tutte le pagine di prodotti
      const allResponses = await this._syncroV2Service.syncAllProducts(
        idCatalog,
        pageSize,
        Number.parseInt(idRootCategory),
        async (pageData: BasicResponse, pageNumber: number, totalPagesFromAPI?: number, totalCountFromAPI?: number) => {
          console.log(`📦 Processando pagina ${pageNumber} prodotti...`);

          if (totalPagesFromAPI !== undefined) {
            totalPages = totalPagesFromAPI;
          }

          if (pageData && pageData.status === 'OK' && pageData.content) {
            const products = Array.isArray(pageData.content) ? pageData.content : [];
            console.log(`📦 Pagina ${pageNumber}: ricevuti ${products.length} prodotti`);

            // Salva i prodotti nel database
            if (products.length > 0) {
              await this._syncroV2DbService.saveProductsV2(
                products,
                idCatalog,
                pageNumber,
                Date.now(),
                idRootCategory
              );
            }

            totalProductsProcessed += products.length;

            // Notifica il progresso
            if (onProgress) {
              onProgress({
                currentPage: pageNumber,
                categoriesProcessed: 0,
                productsProcessed: totalProductsProcessed,
                deletedCategoriesProcessed: 0,
                isComplete: false,
                totalPages: totalPages,
                totalCount: totalCountFromAPI
              });
            }
          } else {
            console.warn(`⚠️ Pagina ${pageNumber}: risposta non valida`, pageData);
          }
        }
      );

      // Segna la fine della sincronizzazione
      const endTime = Date.now();
      await this._syncroV2DbService.markSyncComplete(idCatalog, endTime, totalPages);

      return {
        success: true,
        totalCategoriesProcessed: 0,
        totalProductsProcessed,
        totalDeletedCategoriesProcessed: 0,
        totalPages,
        duration: endTime - startTime
      };

    } catch (error) {
      console.error('❌ Errore durante la sincronizzazione prodotti:', error);
      return {
        success: false,
        totalCategoriesProcessed: 0,
        totalProductsProcessed,
        totalDeletedCategoriesProcessed: 0,
        totalPages,
        duration: Date.now() - startTime,
        error: error.message
      };
    }
  }

  /**
   * Pulisce i dati di sincronizzazione obsoleti
   * @param idCatalog ID del catalogo
   * @param daysOld Numero di giorni di anzianità per considerare i dati obsoleti
   */
  async cleanupOldData(idCatalog: number, daysOld: number = 30): Promise<void> {
    const cutoffTimestamp = Date.now() - (daysOld * 24 * 60 * 60 * 1000);
    await this._syncroV2DbService.cleanupOldSyncData(idCatalog, cutoffTimestamp);
  }
}
