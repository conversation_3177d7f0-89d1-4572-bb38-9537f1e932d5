import { animate, style, transition, trigger } from '@angular/animations';
import { CommonModule, Location } from '@angular/common';
import { ChangeDetectorRef, Component, CUSTOM_ELEMENTS_SCHEMA, EventEmitter, HostListener, Input, OnDestroy, OnInit, Output, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { IonicModule, LoadingController, PopoverController, ToastController } from '@ionic/angular';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { Cart } from 'src/app/service/data/cart';
import { Category, Image } from 'src/app/service/data/category';
import { Document, Product } from 'src/app/service/data/product';
import { LocalStorageService } from 'src/app/service/local-storage.service';
import { DbService } from 'src/app/shared/db.service';
import Utils from 'src/app/shared/utils';
import { Platform } from '@ionic/angular';
import { BehaviorSubject, distinctUntilChanged, fromEvent, merge, Observable, of, Subject } from 'rxjs';
import { CatalogCard } from 'src/app/service/data/catalog-card';
import { select, Store } from '@ngrx/store';
import { addNavHistory, removeLastNavHistory } from 'src/app/store/actions/nav-history.actions';
import { setCurrentCustomer } from 'src/app/store/actions/current-customer.actions';
import { setCart } from 'src/app/store/actions/cart.actions';
import { DocumentService } from 'src/app/service/document/document.service';
import { Selection } from 'src/app/shared/selection';
import { addSelection, resetNavStack, reverseSelection } from 'src/app/store/actions/nav-stack.actions';
import { Favorite } from 'src/app/service/data/favorite';
import { ProductInCart } from 'src/app/service/data/product-in-cart';
import { Subcategory } from 'src/app/service/data/subcategory';
import { QuantityPopoverComponent } from '../../quantity-popover/quantity-popover.component';
import { Keyboard } from '@capacitor/keyboard';
import { Share } from '@capacitor/share';
import { Browser } from '@capacitor/browser';
import { Network } from '@capacitor/network';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { SearchComponent } from '../../search/search.component';
import { LargeCatalogCardComponent } from '../catalog-card/large-catalog-card/large-catalog-card.component';
import { MediumCatalogCardComponent } from '../catalog-card/medium-catalog-card/medium-catalog-card.component';
import { SmallCatalogCardComponent } from '../catalog-card/small-catalog-card/small-catalog-card.component';
import { NavigationPanelComponent } from '../navigation-panel/navigation-panel.component';
import { NavigationQueueComponent } from '../navigation-queue/navigation-queue.component';
import { ArticleComponent } from './article/article.component';
import { RightBarComponent } from './right-bar/right-bar.component';
import { SafePipe } from 'src/app/shared/safe-pipe';
import { ZeroRemoverPipe } from 'src/app/shared/zero-remover.pipe';
import { ProductQuantityPipe } from 'src/app/shared/product-quantity';
import { FilterReferencesPipe } from 'src/app/shared/filter-references-pipe';
type Reference = { tab: string, name: string, idSubCategory: string, image: string, focus: string };

@Component({
    selector: 'app-product',
    templateUrl: './product.component.html',
    styleUrls: ['./product.component.scss'],
    schemas: [CUSTOM_ELEMENTS_SCHEMA],
    animations: [
        trigger('datacolPopup', [
            transition('void => *', [
                style({ opacity: 0 }),
                animate(200, style({ opacity: 1 }))
            ]),
            transition('* => void', [
                animate(200, style({ opacity: 0 }))
            ])
        ])
    ],
    standalone: true,
    imports: [
      CommonModule,
      IonicModule,
      FormsModule,
      ReactiveFormsModule,
      TranslateModule,
      SafePipe,
      ZeroRemoverPipe,
      ProductQuantityPipe,
      FilterReferencesPipe
    ]
})
export class ProductComponent implements OnInit, OnDestroy {
  @Input() datacolCategory: CatalogCard | null = null;
  catalogCard: CatalogCard | null = null;
  datacolCategoryImage: Image[] = [];
  @Input() code: string = null;
  products$: BehaviorSubject<(Product & { inCart: boolean })[]> = new BehaviorSubject<(Product & { inCart: boolean })[]>([]);
  productsImage: string[] = [];
  references$: Subject<Reference[]> = new Subject<any[]>();
  referencesTabs$: Observable<string[]>;
  referencesImage: string[] = [];
  isFilterShown: boolean = false;
  isArticleShown: boolean = false;
  @Input() isPricesShown: boolean = false;
  isArticleWithAttributesShown: boolean = false;
  selectedArticle: Product | null = null;
  @Input() isInfoShown: boolean = false;
  isDownloadShown: boolean = false;
  isVideoPlayerShown: boolean = false;
  isImageViewerShown: boolean = false;
  itemLabel = this._translate.instant('PRODUCT.ITEM');
  descriptionLabel = this._translate.instant('PRODUCT.DESCRIPTION');
  @Input() customerUid: string | null = null;
  @Input() customerName: string | null = null;
  @Input() isFavorite: boolean = false;
  @Input() isProspect: boolean = false;
  resolvedDatacolCategoryImage: string | null = null;
  documentsForDownload: Document[] = [];
  articleCode: string = '';
  imageDirectory: string = localStorage.getItem("imageDirectory");
  appIsOnline$: Observable<boolean> = undefined;
  loadOtherImages = false;
  @Input() showNavigator: boolean = false;
  imageToShow: string | null = null;
  productInCart: ProductInCart[] = [];
  cart: BehaviorSubject<Cart> = new BehaviorSubject<Cart>(null);
  private favorites = [];
  @Output() showArticleOut = new EventEmitter();
  @Output() showArticleWithAttributeOut = new EventEmitter();
  @Output() showDocumentOut = new EventEmitter();
  @Output() openVideoOut = new EventEmitter();
  @Output() openImageViewerOut = new EventEmitter();
  slideOpts = {
    slidesPerView: 6,
    spaceBetween: 10,
    breakpoints: {
      // when window width is >= 320px
      320: {
        slidesPerView: 1,
      },
      // when window width is >= 480px
      480: {
        slidesPerView: 1,
      },
      // when window width is >= 640px
      640: {
        slidesPerView: 6,
      }
    }
  };

  @HostListener('click', ['$event'])
  onClick(event: MouseEvent) {
    // sovrascrive l'evento di click su anchor del contenuto per aprirle l'url esterno
    if ((event['path'] && event['path'][0].localName === 'a') || (event['srcElement'] && event['srcElement']['localName'] === 'a')) {
      event.preventDefault();
      if (event['srcElement'] && event['srcElement']['href'] !== '')
        this.openWindow(event['srcElement']['href']);
      else if (event['path'] && event['path'][0].innerHTML !== '')
        this.openWindow(event['path'][0].innerHTML);
    }
  }

  constructor(private _router: Router, private _platform: Platform, private _activatedRoute: ActivatedRoute, private _location: Location, private _translate: TranslateService, public _loadingController: LoadingController,
    private _toastController: ToastController, private _documentService: DocumentService, private _dbService: DbService, private _lsService: LocalStorageService,
    private _cdref: ChangeDetectorRef, private _store: Store<any>,
    private popoverController: PopoverController) {
    this._router.routeReuseStrategy.shouldReuseRoute = function () {
      return false;
    };
  }

  async openWindow(url: string) {
    await Browser.open({ url });
  }

  getArticles() {
    return this.products$.getValue();
  }

  async ngOnInit() {
    console.log('ProductComponent ngOnInit - datacolCategory:', this.datacolCategory);
    console.log('ProductComponent ngOnInit - code:', this.code);
    
    // Get the idRootCategory from the query parameters
    this._activatedRoute.queryParams.subscribe(params => {
      const idRootCategory = params['idRootCategory'];
      console.log('ProductComponent: idRootCategory from query params:', idRootCategory);
      
      if (!this.datacolCategory) {
        console.error('ProductComponent: datacolCategory is null or undefined');
        return;
      }
      
      // Parse the current parameter if it's a string
      if (typeof this.datacolCategory === 'string') {
        try {
          this.datacolCategory = JSON.parse(this.datacolCategory);
          console.log('Parsed datacolCategory:', this.datacolCategory);
        } catch (e) {
          console.error('Failed to parse datacolCategory:', e);
          return;
        }
      }
      
      // If idRootCategory is not set in datacolCategory, use the one from query params
      if (!this.datacolCategory.idRootCategory && idRootCategory) {
        this.datacolCategory.idRootCategory = idRootCategory;
        console.log('Set idRootCategory from query params:', idRootCategory);
      }
      
      if (!this.datacolCategory.idRootCategory || !this.datacolCategory.id) {
        console.error('ProductComponent: Missing required properties in datacolCategory:', {
          idRootCategory: this.datacolCategory.idRootCategory,
          id: this.datacolCategory.id
        });
        return;
      }

      if (this._platform.is('capacitor')) {
        Keyboard.addListener('keyboardWillShow', (info) => {
          this.adjustPopoverPosition(info.keyboardHeight);
        });

        Keyboard.addListener('keyboardWillHide', () => {
          this.resetPopoverPosition();
        });
      }

      let navStack = [];
      this._store.pipe(select('navStack')).subscribe(res => navStack = res).unsubscribe();
      navStack = navStack.map((roba) => (roba.item as Selection).id);

      // Use the idRootCategory from datacolCategory when creating the catalogCard
      this.catalogCard = new CatalogCard(
        this.datacolCategory.id, 
        this.datacolCategory.idRootCategory, 
        this.datacolCategory.name, 
        this.datacolCategory.image, 
        this.datacolCategory.isProduct, 
        'PRODUCT', 
        [], 
        this.isFavorite, 
        this.datacolCategory.description, 
        null
      );
      
      this._store.dispatch(setCurrentCustomer({ customerData: { uid: this.customerUid, name: this.customerName, isProspect: this.isProspect } }));

      if (!!this.catalogCard && !!this.catalogCard.idRootCategory) {
        var _this = this;
        let subcategories = [];
        this._store.pipe(select('subcategories')).subscribe(res => subcategories = res).unsubscribe();
        const filtered = subcategories.filter((item: Subcategory) => item.idsubcategory.toString() === _this.catalogCard.id.toString());
        if (filtered.length > 0) {
          this.catalogCard.image = filtered[0].image;
          const secondary = Utils.objToJson(filtered[0].images);
          const images = secondary.filter(item => item.format === 'zoom' && item.mediaTypology !== '01Principale').sort(function (a: any, b: any) {
            return Number(a.mediaTypology.substring(0, 2)) <= Number(b.mediaTypology.substring(0, 2)) && a.positionMc <= b.positionMc;
          });
          this.datacolCategoryImage = this.obtainFilteredImage(images);
        }
      }

      this._platform.ready().then(() => {
        if (this._platform.is('capacitor')) {
          Network.getStatus().then(status => {
            this.loadOtherImages = status.connected;
          });
        } else
          this.loadOtherImages = true;
      });
      Utils.checkInternet().then(isOnline => {
        this.loadOtherImages = isOnline
        this._cdref.detectChanges();
      });
      this.getAllData().then(async _ => {
        if (!!this.catalogCard && !!this.catalogCard.image && this.catalogCard.image !== "null") {
          const productImage = this.catalogCard.image.substring(this.catalogCard.image.lastIndexOf('/') + 1).replace('jpg', 'webp');
          this.resolvedDatacolCategoryImage = await Utils.resolveImageUrl(this._platform, this.imageDirectory, productImage);
        }
      });

      this.products$.subscribe(async (products: Product[]) => {
        const review = await Promise.all((products as Product[]).map(async (item) => {
          if (!!item.image) {
            const resolved = await Utils.resolveImageUrl(this._platform, this.imageDirectory, item.image)
            return !!resolved ? resolved : null;
          }
          else
            return null;
        }));
        this.productsImage = review;
        const referenceCodesByTabs = [].concat(...products.map(product => {
          if (!!product.productReferences) {
            const productReferences = Utils.objToJson(product.productReferences);
            const codes = productReferences.map(ref => { return { tab: ref.referenceType, target: ref.target } }).map(ref => { return { tab: ref.tab, code: ref.target.code } });
            if (codes.length > 0)
              return codes;
            else
              return null;
          } else
            return null;
        })).filter((x) => x !== null);
        const referenceCodes = referenceCodesByTabs.map((item) => { return item.code });

        if (referenceCodes.length > 0) {
          await this._dbService.getRecordsByINCondition('products', { key: 'code', value: referenceCodes }).then(async (data) => {
            var products: { tab: string, name: string, idSubCategory: string, image: string, focus: string, divisionStatusCode: string }[] = [];
            data.forEach((product: Product) => {
              const tab = referenceCodesByTabs.find(item => item.code === product.code);
              if (!!tab && !!tab.tab)
                products.push({ tab: tab.tab, name: product.name, idSubCategory: product.idSubCategory, image: product.image, focus: product.focus, divisionStatusCode: product.divisionStatusCode });
            });
            this.references$.next(products);
          }).catch(() => {
            this.references$.next([]);
          });
        } else {
          this.references$.next([]);
        }
      });
      this.references$
        .pipe(distinctUntilChanged((prev, curr) => {
          return JSON.stringify(curr as Reference[]) === JSON.stringify(prev as Reference[])
        }))
        .subscribe(async products => {
          this.referencesTabs$ = of([...new Set(products.map(item => item.tab))]);
          const review = await Promise.all((products).map(async (item) => {
            if (!!item.image) {
              const resolved = await Utils.resolveImageUrl(this._platform, this.imageDirectory, item.image)
              return !!resolved ? resolved : null;
            }
            else
              return null;
          }));
          this.referencesImage = review;
        });
    });
  }

  adjustPopoverPosition(keyboardHeight: number) {
    const popoverElement = document.querySelector('.quantity-popover');
    if (popoverElement) {
      popoverElement.setAttribute('style', `--offset-y: -${keyboardHeight / 2}px !important;`);
    }
  }

  resetPopoverPosition() {
    const popoverElement = document.querySelector('.quantity-popover');
    if (popoverElement) {
      popoverElement.setAttribute('style', '');
    }
  }

  identify(index: number, item: Product) {
    return item.code;
  }

  private obtainFilteredImage(images: Image[]) {
    if (images.length > 0) {
      const shorter = images.map(item => {
        const short = item.url.replace('https://web.datacol.com/imghyb/', '').substring(2); // può esserci 'Q/', 'L/', 'B/' etc.
        const size = short.substring(0, short.indexOf('W'));
        return { size: size, name: item.url.substring(item.url.lastIndexOf('/') + 1), url: item.url, shortCaption: item.shortCaption }
      });
      const groupped = shorter.reduce((hash: any, obj: any) => ({ ...hash, [obj['name']]: (hash[obj['name']] || []).concat(obj) }), {});

      Object.keys(groupped).forEach(function (k) {
        groupped[k] = groupped[k].sort((a: any, b: any) => parseInt(b.size) - parseInt(a.size));
      });

      let secondaryImages = [];
      Object.keys(groupped).forEach(function (k) {
        secondaryImages.push(groupped[k][0]);
      });
      return secondaryImages;
    }
    return [];
  }

  private async getAllData() {
    await this._dbService.createIndex('products', ['idSubCategory']);
    await this._dbService.createIndex('products', ['code']);
    const loading = await this._loadingController.create({
      message: this._translate.instant('GENERICS.WAIT'),
    });
    loading.present();

    this.loadCart();

    await this._dbService.getRecordsByANDCondition('products', [{ key: 'idCategory', value: this.datacolCategory.idRootCategory },
                                                                { key: 'idSubCategory', value: this.datacolCategory.id }]).then(async (data) => {
      const products = (await Promise.all((data as Product[]).map(async (item) => {
        item.documents = Utils.objToJson(item.documents);
        return item;
      }))).map((item) => {
        return { ...item, inCart: this.isProductInCart(item.code) };
      })
      this.products$.next(products);
    }).finally(() => {
      loading.dismiss();
    });
  }

  private async loadCart() {
    if (!!this.customerUid && !this.isProspect) {
      await this._dbService.getRecordsByANDCondition('carts', [{ key: 'agentCode', value: this._lsService.get("customerAgentCode") }, { key: 'idCustomer', value: this.customerUid }]).then(async (data: Cart[]) => {
        if (!!data && data.length > 0 && !!data[0]) {
          this.cart.next({ ...data[0] });
          if (!!data[0].products)
            this.productInCart = Utils.objToJson(data[0].products);
        } else {
          this.productInCart = [];
        }
      });
    }
  }

  openVideo(product: Product) {
    this.openVideoOut.emit(product);
  }

  openPdf(product: Product) {
    this.showDocumentOut.emit(product);
  }

  async share(item: Product) {
    // es: https://eshop.datacol.com/store/datacol/it/EUR/p/YAC100030
    const url = this._documentService.getSharableUrl(item.code, 'p');
    await Share.share({
      title: item.name,
      text: item.name,
      url
    });
  }

  async goToReference(item) {
    const loading = await this._loadingController.create({
      message: this._translate.instant('GENERICS.WAIT'),
    });
    loading.present();

    this._store.dispatch(resetNavStack());
    const lastCategory: Category = await this.getProductFromHierarchy(item.idSubCategory).then((lastCategory) => lastCategory);

    let navStack = [];
    this._store.pipe(select('navStack')).subscribe(res => navStack = res).unsubscribe();
    navStack = navStack.map((roba) => (roba.item as Selection).id);
    let favorites: Favorite[] = [];
    this._store.pipe(select('favorites')).subscribe(res => favorites = [...res]).unsubscribe();
    const found = favorites.filter((item: Favorite) => item.customerUid === this.customerUid && item.idCategory === lastCategory.id).length === 1;
    const isFavorite = this.isProspect ? false : found;
    const datacolCategory = new CatalogCard(lastCategory.id, navStack[0], lastCategory.name, lastCategory.image, lastCategory.isProduct,
      'CATEGORY', Utils.objToJson(lastCategory.subcategories), isFavorite, lastCategory.description, null); // FIXME: focus
    this._store.dispatch(addNavHistory({
      item: {
        type: 'PRODUCT', id: item.idSubCategory.toString(), path: navStack,
        datacolCategory: { category: datacolCategory, isFavorite: isFavorite }
      }
    }));
    this._router.navigate([`/private/catalog/products-carousel/${item.idSubCategory}`], {
      queryParams: {
        idRootCategory: datacolCategory.idRootCategory,
        current: JSON.stringify(datacolCategory),
        customerUid: this.customerUid,
        customerName: this.customerName,
        isFavorite: isFavorite,
        isProspect: this.isProspect,
      }
    }).then(() => {
      loading.dismiss();
    });
  }

  productGoComponentGoBack() {
    let navStackSize = 0;
    const navigationQueue$ = this._store.pipe(select('navStack'));
    navigationQueue$.subscribe(res => navStackSize = res.length).unsubscribe();
    if (navStackSize === 0) {
      let routingStack = [];
      this._store.pipe(select('routingStack')).subscribe(res => routingStack = [...res]).unsubscribe();
      let condition = routingStack.length > 0 && routingStack[routingStack.length - 1].component.includes('catalog');
      while (condition) {
        routingStack.pop();
        condition = routingStack.length > 0 && routingStack[routingStack.length - 1].component.includes('catalog');
      }
      if (routingStack.length > 0)
        this._router.navigate([routingStack[0].component]);
      else
        this._router.navigate(['/private/home']);
    } else {
      let navHistory = [];
      this._store.pipe(select('navHistory')).subscribe(res => navHistory = res).unsubscribe();
      if (navHistory.length === 0) {
        this._router.navigate(['/private/home']);
      }
      else {
        if (navHistory.length >= 2 && navHistory[navHistory.length - 1].item.type === 'PRODUCT' && navHistory[navHistory.length - 2].item.type === 'PRODUCT') {
          const lastHistory = navHistory[navHistory.length - 2];
          this._store.dispatch(removeLastNavHistory());
          this._store.dispatch(resetNavStack());
          this.setNewHierarchy(lastHistory.item.path);

          this._router.navigate([`/private/catalog/products-carousel/${lastHistory.item.id}`], {
            queryParams: {
              idRootCategory: lastHistory.item.datacolCategory.category.idRootCategory,
              current: JSON.stringify(lastHistory.item.datacolCategory.category),
              customerUid: this.customerUid,
              customerName: this.customerName,
              isFavorite: lastHistory.item.datacolCategory.isFavorite,
              isProspect: this.isProspect,
            }
          });
        } else if (navHistory.length >= 2) {
          this._store.dispatch(removeLastNavHistory());
          const lastNavHistory = navHistory[navHistory.length - 2].item;
          this.goToCategory({ idCategory: lastNavHistory.id });
        } else {
          this._location.back();
        }
      }
    }
  }

  showArticle(product) {
    this.showArticleOut.emit(product);
  }

  openArticleWithAttributes(product) {
    this.showArticleWithAttributeOut.emit(product);
  }

  async refreshArticles() {
    this.loadCart().then(async () => {
      await this._dbService.getRecordsByANDCondition('products', [{ key: 'idSubCategory', value: this.datacolCategory.id }]).then(async (data) => {
        const products = (await Promise.all((data as Product[]).map(async (item) => {
          item.documents = Utils.objToJson(item.documents);
          if (!!item.image) {
            const resolved = await Utils.resolveImageUrl(this._platform, this.imageDirectory, item.image)
            item.image = !!resolved ? resolved : null;
          }
          else
            item.image = null;
          return item;
        }))).map((item) => {
          return { ...item, inCart: this.isProductInCart(item.code) };
        })
        this.products$.next(products);
      })
    });
  }


  async addOneToCart(product: Product, multiQuantity?: number) {
    if (!this.isProspect) {
      var cart: Cart = Object.assign({}, this.cart.getValue() as Cart);
      if (!!cart && !!cart.products) {
        cart.products = Utils.objToJson(cart.products);
        const index = cart.products.findIndex(item => item.idProduct === product.code);
        if (index < 0) {
          cart.products.push({ idProduct: product.code, quantity: (multiQuantity ? multiQuantity : +product.minimumDeliveryQuantity), description: product.name, image: product.image, minimumDeliveryQuantity: +product.minimumDeliveryQuantity });
        } else {
          cart.products[index].quantity = multiQuantity ? multiQuantity : (cart.products[index].quantity + +product.minimumDeliveryQuantity);
        }
        await this._dbService.updateRecord('carts', [{ key: 'id', value: cart.id }], [{ key: 'products', value: JSON.stringify(cart.products) }]).then(async _ => {
          this._store.dispatch(setCart({ cart: { idCustomer: this.customerUid, idCart: +cart.id, currentQuantityInCart: (cart.products.length) } }));
          this.cart.next({ ...cart });

          Utils.showSnack(this._toastController, (this._translate.instant("PRODUCT.ITEM_ADDED") + product.code.replace(/^0+/, '')), this._translate.instant("SETTINGS.DONE"), true);
          await this.loadCart();
          var currentProducts = this.products$.getValue().map((item) => {
            return { ...(item as Product), inCart: this.isProductInCart(item.code) };
          })
          this.products$.next(currentProducts);
        });
      } else {
        var productsInCart = [{ idProduct: product.code, quantity: (multiQuantity ? multiQuantity : +product.minimumDeliveryQuantity), description: product.name, image: product.image, minimumDeliveryQuantity: product.minimumDeliveryQuantity }];
        await this._dbService.insertOrReplace("carts", [{ key: 'agentCode', value: this._lsService.get("customerAgentCode") }, { key: 'idCustomer', value: this.customerUid }],
          ['id', 'agentCode', 'idCustomer', 'products'],
          [this._lsService.get("customerAgentCode"), this.customerUid, JSON.stringify(productsInCart)]).then(async _ => {
            var cartId = -1;
            await this._dbService.getRecordsByANDCondition('carts', [{ key: 'agentCode', value: this._lsService.get("customerAgentCode") }, { key: 'idCustomer', value: this.customerUid }]).then(async (data: Cart[]) => {
              if (!!data[0] && !!data[0]['id']) {
                this.cart.next({ ...data[0] });
                const rows = data[0];
                cartId = rows.id;
              }
            });
            if (cartId > 0) {
              this._store.dispatch(setCart({ cart: { idCustomer: this.customerUid, idCart: cartId, currentQuantityInCart: (productsInCart.length) } }));
              Utils.showSnack(this._toastController, (this._translate.instant("PRODUCT.ITEM_ADDED") + product.code.replace(/^0+/, '')), this._translate.instant("SETTINGS.DONE"), true);
              await this.loadCart();
              var currentProducts = this.products$.getValue().map((item) => {
                return { ...(item as Product), inCart: this.isProductInCart(item.code) };
              })
              this.products$.next(currentProducts);
            }
          }
          );
      }
    }
  }

  async showMultipleQuantity(product: Product) {
    if (document.querySelector('.quantity-popover') == null) {
      const popover = await this.popoverController.create({
        component: QuantityPopoverComponent,
        event: event,
        cssClass: 'quantity-popover',
        translucent: true,
        componentProps: { product }
      });
      popover.onDidDismiss().then((data) => {
        if (!!data.data && !!data.data.newQuantity)
          this.addOneToCart(product, +data.data.newQuantity);
      });

      return await popover.present();
    }
  }

  async goToCategory(event) {
    this._router.navigate(['/private/catalog'], {
      queryParams: {
        customerUid: this.customerUid,
        customerName: this.customerName,
        isProspect: this.isProspect,
        showFavorites: false,
        clickedNavarId: event.idCategory,
        turnAround: true,
        comeFromDC: this.datacolCategory.id
      }
    });
  }

  private setNewHierarchy(navHistoryPath: string[]) {
    let categories = [];
    this._store.pipe(select('categories')).subscribe(res => categories = res).unsubscribe();
    navHistoryPath.forEach(element => {
      const filtered = categories.filter(item => item.id.toString() === element.toString());
      this._store.dispatch(addSelection({ item: { name: filtered[0].name, id: filtered[0].id.toString(), isProduct: filtered[0].isProduct, idRootCategory: navHistoryPath[0] } }));
      categories = Utils.objToJson(filtered[0].subcategories);
    });
  }

  private async getProductFromHierarchy(id: string): Promise<Category> {
    let rootCategories = [];
    this._store.pipe(select('categories')).subscribe(res => rootCategories = res).unsubscribe();
    const category = this.findProductInCategory(rootCategories, id);
    this._store.dispatch(reverseSelection());
    return category;
  }

  private findProductInCategory(categories: Category[], idSubCategory: string): Category | null {
    let navStack = [];
    this._store.pipe(select('navStack')).subscribe(res => navStack = res).unsubscribe();
    navStack = navStack.map((roba) => (roba.item as Selection).id);
    if (categories && categories.length > 0) {
      const list = categories.filter((category: Category) => category.id.toString() === idSubCategory);
      if (!!list && list.length > 0) {
        if (!list[0].isProduct)
          this._store.dispatch(addSelection({ item: { name: list[0].name, id: list[0].id.toString(), isProduct: list[0].isProduct, idRootCategory: navStack[0] } }));
        return list[0];
      }
      else {
        let found = null;
        categories.forEach(async (category: Category) => {
          if (!!found)
            return;
          else {
            const subcategory = !!category.subcategories ? Utils.objToJson(category.subcategories) : [];
            if (subcategory.length > 0) {
              found = this.findProductInCategory(subcategory, idSubCategory);
              if (found && !category.isProduct)
                this._store.dispatch(addSelection({ item: { name: category.name, id: category.id.toString(), isProduct: category.isProduct, idRootCategory: navStack[0] } }));
            }
            else
              found = null;
          }
        });
        return found;
      }
    } else {
      return null;
    }
  }

  openSearch() {
    this.isFilterShown = true;
  }

  openImageViewer(imageUrl) {
    this.openImageViewerOut.emit(imageUrl);
  }

  private isProductInCart(code: string) {
    return !!this.productInCart && this.productInCart.filter(prod => prod.idProduct === code).length > 0;
  }

  ngOnDestroy() {
    if (this._platform.is('capacitor'))
      Keyboard.removeAllListeners();
  }
}
