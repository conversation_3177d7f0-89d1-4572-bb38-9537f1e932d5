import { Injectable, inject } from '@angular/core';
import { Store } from '@ngrx/store';
import { HybridDbService } from 'src/app/shared/hybrid-db.service';
import { Category } from '../data/category';
import { Product } from '../data/product';
import { BasicResponse } from '../data/basic-response';
import { ImageOfflineService } from './image-offline.service';
import { setCategories } from 'src/app/store/actions/categories.actions';
import { setProducts } from 'src/app/store/actions/products.actions';
import { setRootCategories } from 'src/app/store/actions/root-categories.actions';

@Injectable({
  providedIn: 'root'
})
export class SyncroV2DbService {

  private _dbService = inject(HybridDbService);
  private _imageOfflineService = inject(ImageOfflineService);
  private _store = inject(Store<any>);

  constructor() { }

  /**
   * Aggiorna il database con le nuove colonne necessarie per la sincronizzazione v2
   */
  async updateDatabaseSchema(): Promise<void> {
    try {
      // Inizializza il database per le immagini offline
      await this._imageOfflineService.initializeImageDatabase();

      // Aggiungi colonne per le categorie
      await this._dbService.addColumn('categories', 'lastSyncTimestamp', 'TEXT');
      await this._dbService.addColumn('categories', 'idCatalog', 'TEXT');
      await this._dbService.addColumn('categories', 'syncPageNumber', 'TEXT');
      await this._dbService.addColumn('categories', 'syncedWithV2', 'TEXT');
      await this._dbService.addColumn('categories', 'isRootCategoryByRegex', 'TEXT');

      // Aggiungi colonne per i prodotti v2
      await this._dbService.addColumn('products', 'lastSyncTimestamp', 'TEXT');
      await this._dbService.addColumn('products', 'idCatalog', 'TEXT');
      await this._dbService.addColumn('products', 'syncPageNumber', 'TEXT');
      await this._dbService.addColumn('products', 'syncedWithV2', 'TEXT');
      await this._dbService.addColumn('products', 'idRootCategory', 'TEXT');
      await this._dbService.addColumn('products', 'imageOfflineAvailable', 'TEXT');

      console.log('✅ Schema database v2 aggiornato con successo');
    } catch (error) {
      console.error('❌ Errore durante l\'aggiornamento dello schema database v2:', error);
    }
  }

  /**
   * Salva le categorie ricevute dalla sincronizzazione v2
   * @param categories Array di categorie da salvare
   * @param idCatalog ID del catalogo
   * @param pageNumber Numero della pagina sincronizzata
   * @param timestamp Timestamp della sincronizzazione
   */
  async saveCategoriesV2(
    categories: Category[],
    idCatalog: number,
    pageNumber: number,
    timestamp: number
  ): Promise<void> {
    // Validazione robusta dell'input
    if (!categories) {
      console.warn('⚠️ saveCategoriesV2: categories è null o undefined');
      return;
    }

    if (!Array.isArray(categories)) {
      console.error('❌ saveCategoriesV2: categories non è un array', typeof categories, categories);
      throw new Error('categories deve essere un array');
    }

    if (categories.length === 0) {
      console.log('ℹ️ saveCategoriesV2: array categories vuoto, nulla da salvare');
      return;
    }

    console.log(`💾 Salvando ${categories.length} categorie per catalogo ${idCatalog}, pagina ${pageNumber}`);

    // Applica la logica per identificare le root categories tramite regex
    const processedCategories = this.identifyRootCategoriesByRegex(categories);

    const columns: string[] = [];
    const records: { andConditionList: { key: string, value: any }[], values: any[] }[] = [];

    for (const category of processedCategories) {
      // Validazione della singola categoria
      if (!category) {
        console.warn('⚠️ Categoria null o undefined, saltata');
        continue;
      }

      if (!category.id) {
        console.warn('⚠️ Categoria senza ID, saltata:', category);
        continue;
      }

      // Prepara le colonne se non sono già state preparate
      if (columns.length === 0) {
        columns.push(...this.prepareColumns(category));
      }

      try {
        // Prepara i valori per questa categoria
        const values = this.prepareValues(category, idCatalog, pageNumber, timestamp);
        const keys = [{ key: 'id', value: category.id }];

        // Se la categoria non è cancellata, la salviamo
        if (!category.cancellationInstant) {
          records.push({ andConditionList: keys, values: values });
        } else {
          // Se è cancellata, la eliminiamo dal database
          await this._dbService.deleteRecord('categories', keys);
          console.log('Categoria cancellata rimossa:', category.id);
        }
      } catch (error) {
        console.error(`❌ Errore durante l'elaborazione della categoria ${category.id}:`, error);
        // Continua con la prossima categoria invece di interrompere tutto
      }
    }

    // Salva tutti i record in una transazione
    if (records.length > 0) {
      await this._dbService.multipleInsertOrReplaceByTransaction('categories', false, columns, records);
      console.log(`Salvate ${records.length} categorie dalla pagina ${pageNumber} del catalogo ${idCatalog}`);

      // Aggiorna gli store NgRx dopo il salvataggio
      await this.updateCategoriesStore(idCatalog);
    }
  }

  /**
   * Gestisce le categorie cancellate ricevute dalla sincronizzazione v2
   * @param deletedCategories Array di categorie cancellate
   * @param idCatalog ID del catalogo
   * @param pageNumber Numero della pagina sincronizzata
   */
  async handleDeletedCategoriesV2(
    deletedCategories: Category[],
    idCatalog: number,
    pageNumber: number
  ): Promise<void> {
    // Validazione robusta dell'input
    if (!deletedCategories) {
      console.warn('⚠️ handleDeletedCategoriesV2: deletedCategories è null o undefined');
      return;
    }

    if (!Array.isArray(deletedCategories)) {
      console.error('❌ handleDeletedCategoriesV2: deletedCategories non è un array', typeof deletedCategories, deletedCategories);
      throw new Error('deletedCategories deve essere un array');
    }

    if (deletedCategories.length === 0) {
      console.log('ℹ️ handleDeletedCategoriesV2: array deletedCategories vuoto, nulla da eliminare');
      return;
    }

    console.log(`🗑️ Eliminando ${deletedCategories.length} categorie cancellate per catalogo ${idCatalog}, pagina ${pageNumber}`);

    for (const category of deletedCategories) {
      // Validazione della singola categoria
      if (!category) {
        console.warn('⚠️ Categoria cancellata null o undefined, saltata');
        continue;
      }

      if (!category.id) {
        console.warn('⚠️ Categoria cancellata senza ID, saltata:', category);
        continue;
      }

      const keys = [{ key: 'id', value: category.id }];

      try {
        await this._dbService.deleteRecord('categories', keys);
        console.log(`Categoria cancellata rimossa: ${category.id} dalla pagina ${pageNumber} del catalogo ${idCatalog}`);
      } catch (error) {
        console.error(`Errore durante l'eliminazione della categoria ${category.id}:`, error);
      }
    }

    // Aggiorna gli store NgRx dopo l'eliminazione delle categorie
    await this.updateCategoriesStore(idCatalog);
  }

  /**
   * Identifica le root categories tramite regex applicata al campo idApp
   * @param categories Array di categorie da processare
   * @returns Array di categorie con il campo isRootCategoryByRegex valorizzato
   */
  private identifyRootCategoriesByRegex(categories: Category[]): Category[] {
    const rootCategoryRegex = /\b(?:it;)(\d+)\b(?!;[\d])/;
    let rootCategoriesCount = 0;

    const processedCategories = categories.map(category => {
      // Crea una copia della categoria per non modificare l'originale
      const processedCategory = { ...category };

      // Applica la regex al campo idApp per identificare le root categories
      if (processedCategory.idApp && rootCategoryRegex.test(processedCategory.idApp)) {
        processedCategory.isRootCategoryByRegex = true;
        rootCategoriesCount++;
        console.log(`🔍 Root category identificata tramite regex: ${processedCategory.id} (idApp: ${processedCategory.idApp})`);
      } else {
        processedCategory.isRootCategoryByRegex = false;
      }

      return processedCategory;
    });

    console.log(`📊 Identificate ${rootCategoriesCount} root categories tramite regex su ${categories.length} categorie totali`);
    return processedCategories;
  }

  /**
   * Prepara le colonne per l'inserimento nel database
   * @param category Categoria di esempio per estrarre le colonne
   * @returns Array di nomi delle colonne
   */
  private prepareColumns(category: Category): string[] {
    const columns: string[] = [];

    // Aggiungi tutte le proprietà della categoria
    Object.keys(category).forEach(key => {
      if (key !== 'media' && key !== 'images') { // Escludiamo array complessi
        columns.push(key);
      }
    });

    // Aggiungi le nuove colonne per la sincronizzazione v2
    if (!columns.includes('lastSyncTimestamp')) columns.push('lastSyncTimestamp');
    if (!columns.includes('idCatalog')) columns.push('idCatalog');
    if (!columns.includes('syncPageNumber')) columns.push('syncPageNumber');
    if (!columns.includes('syncedWithV2')) columns.push('syncedWithV2');
    if (!columns.includes('isRootCategoryByRegex')) columns.push('isRootCategoryByRegex');

    return columns;
  }

  /**
   * Prepara i valori per l'inserimento nel database
   * @param category Categoria da inserire
   * @param idCatalog ID del catalogo
   * @param pageNumber Numero della pagina
   * @param timestamp Timestamp della sincronizzazione
   * @returns Array di valori
   */
  private prepareValues(
    category: Category,
    idCatalog: number,
    pageNumber: number,
    timestamp: number
  ): any[] {
    const values: any[] = [];

    // Aggiungi tutti i valori della categoria
    Object.entries(category).forEach(([key, value]) => {
      if (key !== 'media' && key !== 'images') {
        if (Array.isArray(value)) {
          values.push(JSON.stringify(value));
        } else if (typeof value === 'object' && value !== null) {
          values.push(JSON.stringify(value));
        } else {
          values.push(value || '');
        }
      }
    });

    // Aggiungi i valori per le nuove colonne
    values.push(timestamp.toString()); // lastSyncTimestamp
    values.push(idCatalog.toString()); // idCatalog
    values.push(pageNumber.toString()); // syncPageNumber
    values.push('true'); // syncedWithV2
    values.push(category.isRootCategoryByRegex ? 'true' : 'false'); // isRootCategoryByRegex

    return values;
  }

  /**
   * Ottiene le root categories identificate tramite regex dal database
   * @param idCatalog ID del catalogo (opzionale)
   * @returns Promise<Category[]> Array delle root categories
   */
  async getRootCategoriesByRegex(idCatalog?: number): Promise<Category[]> {
    try {
      const conditions: { key: string, value: any }[] = [
        { key: 'isRootCategoryByRegex', value: 'true' },
        { key: 'syncedWithV2', value: 'true' }
      ];

      if (idCatalog !== undefined) {
        conditions.push({ key: 'idCatalog', value: idCatalog.toString() });
      }

      const result = await this._dbService.getRecordsByANDCondition('categories', conditions);
      console.log(`🔍 getRootCategoriesByRegex: trovate ${result.length} root categories identificate tramite regex`);
      return result as Category[];
    } catch (error) {
      console.error('❌ Errore nel recupero delle root categories tramite regex:', error);
      return [];
    }
  }

  /**
   * Ottiene l'ultimo timestamp di sincronizzazione per un catalogo
   * @param idCatalog ID del catalogo
   * @returns Promise<number | null> Ultimo timestamp o null se non trovato
   */
  async getLastSyncTimestamp(idCatalog: number): Promise<number | null> {
    try {
      const result = await this._dbService.getColumnFromTableByANDCondition(
        'categories',
        ['lastSyncTimestamp'],
        [{ key: 'idCatalog', value: idCatalog.toString() }]
      );

      if (result && result.length > 0) {
        const timestamps = result
          .map(item => parseInt(item.lastSyncTimestamp))
          .filter(ts => !isNaN(ts))
          .sort((a, b) => b - a); // Ordina in modo decrescente

        return timestamps.length > 0 ? timestamps[0] : null;
      }

      return null;
    } catch (error) {
      console.error('Errore durante il recupero dell\'ultimo timestamp di sincronizzazione:', error);
      return null;
    }
  }

  /**
   * Segna l'inizio di una nuova sincronizzazione per un catalogo
   * @param idCatalog ID del catalogo
   * @param timestamp Timestamp di inizio sincronizzazione
   */
  async markSyncStart(idCatalog: number, timestamp: number): Promise<void> {
    try {
      // Qui potresti aggiungere una tabella di log per tracciare le sincronizzazioni
      console.log(`Inizio sincronizzazione v2 per catalogo ${idCatalog} al timestamp ${timestamp}`);
    } catch (error) {
      console.error('Errore durante il marking dell\'inizio sincronizzazione:', error);
    }
  }

  /**
   * Segna la fine di una sincronizzazione per un catalogo
   * @param idCatalog ID del catalogo
   * @param timestamp Timestamp di fine sincronizzazione
   * @param totalPages Numero totale di pagine sincronizzate
   */
  async markSyncComplete(idCatalog: number, timestamp: number, totalPages: number): Promise<void> {
    try {
      console.log(`Completata sincronizzazione v2 per catalogo ${idCatalog} al timestamp ${timestamp}. Pagine sincronizzate: ${totalPages}`);
    } catch (error) {
      console.error('Errore durante il marking della fine sincronizzazione:', error);
    }
  }

  /**
   * Pulisce i dati di sincronizzazione obsoleti per un catalogo
   * @param idCatalog ID del catalogo
   * @param olderThanTimestamp Timestamp prima del quale eliminare i dati
   */
  async cleanupOldSyncData(idCatalog: number, olderThanTimestamp: number): Promise<void> {
    try {
      // Elimina le categorie sincronizzate prima del timestamp specificato
      const query = `DELETE FROM categories WHERE idCatalog = '${idCatalog}' AND lastSyncTimestamp < '${olderThanTimestamp}'`;
      // Nota: questo richiederebbe un metodo personalizzato nel DbService per query complesse
      console.log(`Cleanup dei dati di sincronizzazione per catalogo ${idCatalog} prima del timestamp ${olderThanTimestamp}`);
    } catch (error) {
      console.error('Errore durante il cleanup dei dati di sincronizzazione:', error);
    }
  }

  /**
   * Salva i prodotti ricevuti dalla sincronizzazione v2
   * @param products Array di prodotti da salvare
   * @param idCatalog ID del catalogo
   * @param pageNumber Numero della pagina sincronizzata
   * @param timestamp Timestamp della sincronizzazione
   * @param idRootCategory ID della categoria radice (opzionale)
   */
  async saveProductsV2(
    products: Product[],
    idCatalog: number,
    pageNumber: number,
    timestamp: number,
    idRootCategory?: string
  ): Promise<void> {
    // Validazione robusta dell'input
    if (!products) {
      console.warn('⚠️ saveProductsV2: products è null o undefined');
      return;
    }

    if (!Array.isArray(products)) {
      console.error('❌ saveProductsV2: products non è un array', typeof products, products);
      throw new Error('products deve essere un array');
    }

    if (products.length === 0) {
      console.log('📦 Nessun prodotto da salvare per la pagina', pageNumber);
      return;
    }

    console.log(`💾 Salvataggio ${products.length} prodotti per catalogo ${idCatalog}, pagina ${pageNumber}`);

    try {
      // Prepara i dati per il salvataggio batch
      const columns = this.prepareProductColumns(products[0]);
      const records: { andConditionList: { key: string, value: any }[], values: any[] }[] = [];

      for (const product of products) {
        try {
          // Prepara i valori per questo prodotto
          const values = this.prepareProductValues(product, idCatalog, pageNumber, timestamp, idRootCategory);
          const keys = [{ key: 'code', value: product.code }];

          // Se il prodotto non è cancellato, lo salviamo
          if (!product.cancellationInstant) {
            records.push({ andConditionList: keys, values: values });

            // Scarica l'immagine del prodotto se disponibile
            if (product.image && product.image !== 'null') {
              await this._imageOfflineService.downloadAndSaveImage(
                product.image,
                idCatalog,
                'PRODUCT',
                product.code,
                idRootCategory
              );
            }
          } else {
            // Se è cancellato, lo eliminiamo dal database
            await this._dbService.deleteRecord('products', keys);
            console.log('Prodotto cancellato rimosso:', product.code);
          }
        } catch (error) {
          console.error(`❌ Errore durante l'elaborazione del prodotto ${product.code}:`, error);
          // Continua con il prossimo prodotto invece di interrompere tutto
        }
      }

      // Salva tutti i prodotti in batch se ci sono record da salvare
      if (records.length > 0) {
        await this._dbService.addRecords('products', columns, records);
        console.log(`✅ Salvati ${records.length} prodotti per la pagina ${pageNumber}`);

        // Aggiorna gli store NgRx dopo il salvataggio
        await this.updateProductsStore(idCatalog);
      }

    } catch (error) {
      console.error('❌ Errore durante il salvataggio dei prodotti v2:', error);
      throw error;
    }
  }

  /**
   * Prepara le colonne per l'inserimento dei prodotti nel database
   * @param product Prodotto di esempio per estrarre le colonne
   * @returns Array di nomi delle colonne
   */
  private prepareProductColumns(product: Product): string[] {
    const columns: string[] = [];

    // Aggiungi tutte le proprietà del prodotto
    Object.keys(product).forEach(key => {
      if (key !== 'media' && key !== 'images' && key !== 'classifications' &&
          key !== 'documents' && key !== 'priceRows' && key !== 'productReferences') {
        columns.push(key);
      }
    });

    // Aggiungi le nuove colonne per la sincronizzazione v2
    if (!columns.includes('lastSyncTimestamp')) columns.push('lastSyncTimestamp');
    if (!columns.includes('idCatalog')) columns.push('idCatalog');
    if (!columns.includes('syncPageNumber')) columns.push('syncPageNumber');
    if (!columns.includes('syncedWithV2')) columns.push('syncedWithV2');
    if (!columns.includes('idRootCategory')) columns.push('idRootCategory');
    if (!columns.includes('imageOfflineAvailable')) columns.push('imageOfflineAvailable');

    return columns;
  }

  /**
   * Prepara i valori per l'inserimento dei prodotti nel database
   * @param product Prodotto da inserire
   * @param idCatalog ID del catalogo
   * @param pageNumber Numero della pagina
   * @param timestamp Timestamp della sincronizzazione
   * @param idRootCategory ID della categoria radice
   * @returns Array di valori
   */
  private prepareProductValues(
    product: Product,
    idCatalog: number,
    pageNumber: number,
    timestamp: number,
    idRootCategory?: string
  ): any[] {
    const values: any[] = [];

    // Aggiungi tutti i valori del prodotto
    Object.entries(product).forEach(([key, value]) => {
      if (key !== 'media' && key !== 'images' && key !== 'classifications' &&
          key !== 'documents' && key !== 'priceRows' && key !== 'productReferences') {
        if (Array.isArray(value)) {
          values.push(JSON.stringify(value));
        } else if (typeof value === 'object' && value !== null) {
          values.push(JSON.stringify(value));
        } else {
          values.push(value || '');
        }
      }
    });

    // Aggiungi i valori per le nuove colonne
    values.push(timestamp.toString()); // lastSyncTimestamp
    values.push(idCatalog.toString()); // idCatalog
    values.push(pageNumber.toString()); // syncPageNumber
    values.push('true'); // syncedWithV2
    values.push(idRootCategory || ''); // idRootCategory
    values.push(product.image && product.image !== 'null' ? 'true' : 'false'); // imageOfflineAvailable

    return values;
  }

  /**
   * Ottiene l'ultimo timestamp di sincronizzazione per i prodotti di un catalogo
   * @param idCatalog ID del catalogo
   * @param idRootCategory ID della categoria radice (opzionale)
   * @returns Promise<number | null> Ultimo timestamp o null se non trovato
   */
  async getLastProductSyncTimestamp(idCatalog: number, idRootCategory?: string): Promise<number | null> {
    try {
      const conditions = [{ key: 'idCatalog', value: idCatalog.toString() }];
      if (idRootCategory) {
        conditions.push({ key: 'idRootCategory', value: idRootCategory });
      }

      const result = await this._dbService.getColumnFromTableByANDCondition(
        'products',
        ['lastSyncTimestamp'],
        conditions
      );

      if (result && result.length > 0) {
        const timestamps = result
          .map(item => parseInt(item.lastSyncTimestamp))
          .filter(ts => !isNaN(ts))
          .sort((a, b) => b - a); // Ordina in modo decrescente

        return timestamps.length > 0 ? timestamps[0] : null;
      }

      return null;
    } catch (error) {
      console.error('❌ Errore durante il recupero dell\'ultimo timestamp di sincronizzazione prodotti:', error);
      return null;
    }
  }

  /**
   * Aggiorna gli store NgRx delle categorie dopo la sincronizzazione
   */
  private async updateCategoriesStore(idCatalog: number): Promise<void> {
    try {
      // Ottieni tutte le categorie dal database
      const allCategories = await this._dbService.getAll(['categories']);

      // Dispatch action per aggiornare lo store delle categorie
      this._store.dispatch(setCategories({ items: allCategories }));

      // Ottieni e aggiorna le root categories
      const rootCategories = allCategories.filter(cat => cat.isRootCategory === 'true' || cat.isRootCategoryByRegex === 'true');
      this._store.dispatch(setRootCategories({ rootCategories: rootCategories }));

      console.log(`🔄 Store NgRx aggiornato: ${allCategories.length} categorie totali, ${rootCategories.length} root categories`);
    } catch (error) {
      console.error('❌ Errore durante l\'aggiornamento dello store delle categorie:', error);
    }
  }

  /**
   * Aggiorna gli store NgRx dei prodotti dopo la sincronizzazione
   */
  private async updateProductsStore(idCatalog: number): Promise<void> {
    try {
      // Ottieni tutti i prodotti dal database
      const allProducts = await this._dbService.getAll(['products']);

      // Dispatch action per aggiornare lo store dei prodotti
      this._store.dispatch(setProducts({ items: allProducts }));

      console.log(`🔄 Store NgRx aggiornato: ${allProducts.length} prodotti totali`);
    } catch (error) {
      console.error('❌ Errore durante l\'aggiornamento dello store dei prodotti:', error);
    }
  }
}
