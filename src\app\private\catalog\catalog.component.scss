.container {
  width: 100%;

  &:after {
    content: "";
    display: table;
    clear: both;
  }

  .column {
    float: left;
    width: auto;
    max-width: 100%;
  }
}

.next-category-button-spacer {
  height: 60px;
  display: flex;
  align-items: center;
}

.grey {
  background-color: var(--ion-dat-gray);
  --backgroud: var(--ion-dat-gray);
}

#categories {
  width: 100%;
  height: calc(100vh - 135px);
  margin-top: 15px;
  margin-right: 60px;
  border-radius: 3px;
  position: relative;
  z-index: 8000px !important;
}

#categories.subategory {
  background: var(--ion-dat-gray);
  //height: calc(100vh - 120px);
}

.cards {
  height: 90vh;

  swiper-container {
    height: calc(100% - var(--margin-nav));

    .swiper-wrapper {
      margin: auto !important;
    }

    swiper-slide {
      align-items: flex-start;
      padding: 15px;
      width: 100% !important;

      .slider-grid {
        /*height: var(--full-page);*/
        width: 100%;
        display: grid;
        grid-auto-flow: row;
        grid-template-rows: repeat(calc(var(--rows) - 1), minmax(calc((100% / var(--rows)) - 6px), 120px)) minmax(calc(100% / var(--rows)), 120px);
        grid-template-columns: repeat(calc(var(--columns) - 1), minmax(calc((100% / var(--columns)) - 6px), 300px)) minmax(calc((100% / var(--columns))), 300px);
        grid-gap: 6px;
        height: 100%;
      }
    }
  }
}

.customer-name {
  position: absolute;
  bottom: 15px;
  font-weight: bold;
  color: var(--ion-dat-dark-gray);
  text-transform: uppercase;
  z-index: 9998;
  white-space: nowrap;

  img {
    margin-right: 10px;
  }
}

.category-navigation {
  position: fixed;
  bottom: 75px;
  display: flex;
  width: calc(100% - 130px);
  justify-content: space-between;
  padding-right: 15px;
  padding-left: 15px;
  z-index: 9999 !important;

  div {
    align-items: center;
    display: flex;

    span {
      font-weight: bold;
      color: var(--ion-dat-black);
    }

    ion-icon {
      color: var(--ion-dat-red);
      font-size: 20px;
    }

    &:first-child {
      ion-icon {
        margin-left: 5px;
      }

      span {
        margin-left: 10px;
      }
    }

    &:last-child span {
      margin-right: 10px;
    }
  }
}

.category-navigation-infinite {
  margin-bottom: 15px;
  display: flex;
  width: 100%;
  justify-content: flex-end;
  padding-right: 15px;
  padding-left: 15px;
  z-index: 9999 !important;

  div {
    align-items: center;
    display: flex;

    span {
      font-weight: bold;
      color: var(--ion-dat-black);
    }

    ion-icon {
      color: var(--ion-dat-red);
      font-size: 20px;
    }

    &:first-child {
      ion-icon {
        margin-left: 5px;
      }

      span {
        margin-left: 10px;
      }
    }

    &:last-child span {
      margin-right: 10px;
    }
  }
}

.pagination {
  position: absolute;
  background-color: var(--ion-dat-white);
  bottom: 7px;
  z-index: 9999;
  max-width: calc(30%);
  overflow-x: scroll;
  overflow-y: hidden;
  margin: auto;
  display: flex;
  height: 42px;
  left: 0;
  right: 0;
  text-align: center;

  &::-webkit-scrollbar {
    height: 7px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: var(--ion-dat-gray);
    border-radius: 20px;
  }

  .letter {
    padding: 4px;
    margin: 4px;
    width: fit-content;
    float: left;

    div {
      width: 31px;
      padding: 4px;
      font-size: 12px;
    }

    .current {
      background: var(--ion-dat-red);
      padding: 4px;
      border-radius: 30px;
      color: var(--ion-dat-white);
    }

    .empty {
      color: var(--ion-dat-gray)
    }
  }
}

/* TABLET orizzontale
@media screen and (min-width: 501px) and (max-width: 850px) and (orientation: portrait) {
  .pagination {
    bottom: 77px !important;
  }
}
 */


/* TABLET verticale */
@media screen and (orientation: portrait) {
  .customer-name {
    width: 30% !important;
    position: fixed !important;
  }
}

@supports (-webkit-touch-callout: none) {

  /* CSS specific to iOS devices */
  .pagination {
    bottom: 0px !important;
  }

  .slider-grid {
    height: calc(100% - 16px) !important;
  }

  app-large-catalog-card {
    height: calc(100vh - 152px - 16px) !important;
  }

  app-medium-catalog-card {
    height: calc(50vh - 77px - 16px) !important;
  }
}


app-navigation-panel {
  display: none;

  &.visible {
    display: inherit;
  }
}

app-large-catalog-card {
  display: flex;
  height: calc(100vh - 152px); // la vista è intera ma tolgo i margini
}

app-medium-catalog-card {
  display: flex;
  height: calc(50vh - 77px); // la vista è divisa in due, tolgo i margini
}

.m-auto {
  width: 100%;
  height: 100%;
  margin: auto;
}
.ion-infinite-row {
}

.center {
  display: flex;
  justify-content: center;
  align-items: center;
}

ion-grid {
  display: flex;
  justify-content: center;
  width: calc(calc(190px *  var(--columns) + calc(14px * var(--columns))));
}

#categories ion-content{
  --background: var(--ion-dat-gray);
}