import { Injectable } from '@angular/core';
import { TableProperty } from './table-property';

@Injectable({
  providedIn: 'root'
})
export class IndexedDbService {
  private dbName = 'datacol_db';
  private dbVersion = 2;
  private db: IDBDatabase | null = null;

  constructor() {}

  /**
   * Forza la ricreazione del database eliminando quello esistente
   */
  async recreateDatabase(): Promise<void> {
    try {
      // Chiudi la connessione esistente
      if (this.db) {
        this.db.close();
        this.db = null;
      }

      // Elimina il database esistente
      return new Promise((resolve, reject) => {
        const deleteRequest = indexedDB.deleteDatabase(this.dbName);

        deleteRequest.onsuccess = () => {
          this.dbVersion = 2; // Reset alla versione corrente
          resolve();
        };

        deleteRequest.onerror = () => {
          console.error('❌ Errore nell\'eliminazione del database IndexedDB');
          reject(new Error('Failed to delete IndexedDB database'));
        };

        deleteRequest.onblocked = () => {
          console.warn('⚠️ Eliminazione del database bloccata, chiudere tutte le schede');
          // Prova comunque a continuare
          setTimeout(() => resolve(), 1000);
        };
      });
    } catch (error) {
      console.error('❌ Errore durante la ricreazione del database:', error);
      throw error;
    }
  }

  /**
   * Verifica se IndexedDB è disponibile e funzionante
   */
  async isAvailable(): Promise<boolean> {
    try {
      if (!window.indexedDB) {
        console.warn('IndexedDB is not supported in this browser');
        return false;
      }

      // Test di apertura di un database temporaneo
      const testDbName = 'test_db_availability';
      const request = indexedDB.open(testDbName, 1);

      return new Promise((resolve) => {
        const timeout = setTimeout(() => {
          console.warn('IndexedDB availability test timed out');
          resolve(false);
        }, 5000);

        request.onerror = () => {
          clearTimeout(timeout);
          console.warn('IndexedDB is not available:', request.error);
          resolve(false);
        };

        request.onsuccess = () => {
          clearTimeout(timeout);
          const db = request.result;
          db.close();

          // Elimina il database di test
          const deleteRequest = indexedDB.deleteDatabase(testDbName);
          deleteRequest.onsuccess = () => {
            console.log('IndexedDB is available and working');
            resolve(true);
          };
          deleteRequest.onerror = () => {
            console.log('IndexedDB is available but cleanup failed');
            resolve(true); // Comunque funziona
          };
        };

        request.onupgradeneeded = (event) => {
          // Crea una tabella di test
          const db = (event.target as IDBOpenDBRequest).result;
          if (!db.objectStoreNames.contains('test')) {
            db.createObjectStore('test', { keyPath: 'id' });
          }
        };
      });
    } catch (error) {
      console.warn('Error checking IndexedDB availability:', error);
      return false;
    }
  }

  private async openDatabase(): Promise<IDBDatabase> {
    if (this.db) {
      return this.db;
    }

    return new Promise((resolve, reject) => {
      // Verifica se IndexedDB è disponibile
      if (!window.indexedDB) {
        reject(new Error('IndexedDB is not supported in this browser'));
        return;
      }

      const request = indexedDB.open(this.dbName, this.dbVersion);

      request.onerror = (event) => {
        console.error('IndexedDB error:', event);
        const error = request.error;
        let errorMessage = 'Failed to open IndexedDB';

        if (error) {
          switch (error.name) {
            case 'QuotaExceededError':
              errorMessage = 'Storage quota exceeded. Please free up some space.';
              break;
            case 'VersionError':
              errorMessage = 'Database version error. Please refresh the page.';
              break;
            case 'InvalidStateError':
              errorMessage = 'Database is in an invalid state. Please refresh the page.';
              break;
            case 'UnknownError':
              errorMessage = 'Unknown database error. This might be due to private browsing mode.';
              break;
            default:
              errorMessage = `Database error: ${error.name} - ${error.message}`;
          }
        }

        reject(new Error(errorMessage));
      };

      request.onsuccess = () => {
        this.db = request.result;

        // Aggiungi listener per errori del database
        this.db.onerror = (event) => {
          console.error('Database error:', event);
        };

        this.db.onversionchange = () => {
          console.warn('Database version changed, closing connection');
          this.db?.close();
          this.db = null;
        };

        resolve(this.db);
      };

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;

        try {
          // Crea le tabelle principali se non esistono
          const tableNames = [
            'customers', 'categories', 'subcategories', 'datacolCategories',
            'products', 'carts', 'userSettings', 'prospectTypes', 'favorites', 'discounts'
          ];

          tableNames.forEach(tableName => {
            if (!db.objectStoreNames.contains(tableName)) {
              const store = db.createObjectStore(tableName, { keyPath: 'id', autoIncrement: false });

              // Crea indici comuni
              try {
                if (tableName === 'customers') {
                  store.createIndex('uid', 'uid', { unique: true });
                } else if (tableName === 'subcategories') {
                  store.createIndex('idrootcategory_idsubcategory', ['idrootcategory', 'idsubcategory'], { unique: true });
                } else if (tableName === 'products') {
                  store.createIndex('idSubCategory', 'idSubCategory', { unique: false });
                  store.createIndex('code', 'code', { unique: false });
                } else if (tableName === 'userSettings') {
                  store.createIndex('username_type', ['username', 'type'], { unique: true });
                } else if (tableName === 'prospectTypes') {
                  store.createIndex('uid_name_catalog', ['uid', 'name', 'catalog'], { unique: true });
                }
              } catch (indexError) {
                console.warn(`Failed to create index for ${tableName}:`, indexError);
              }
            }
          });
        } catch (upgradeError) {
          console.error('Error during database upgrade:', upgradeError);
          reject(new Error(`Database upgrade failed: ${upgradeError.message}`));
        }
      };

      // Timeout per evitare attese infinite
      setTimeout(() => {
        if (!this.db) {
          reject(new Error('Database connection timeout'));
        }
      }, 10000); // 10 secondi di timeout
    });
  }

  async createTable(tableName: string, columnList: TableProperty[], withAutomaticId: boolean = false): Promise<any> {
    
    const db = await this.openDatabase();
    
    // Se la tabella non esiste, la creiamo durante l'upgrade
    if (!db.objectStoreNames.contains(tableName)) {
      // Chiudi la connessione corrente e riapri con versione incrementata
      db.close();
      this.db = null;
      this.dbVersion++;
      
      return new Promise((resolve, reject) => {
        const request = indexedDB.open(this.dbName, this.dbVersion);
        
        request.onupgradeneeded = (event) => {
          const upgradeDb = (event.target as IDBOpenDBRequest).result;
          if (!upgradeDb.objectStoreNames.contains(tableName)) {
            const store = upgradeDb.createObjectStore(tableName, { 
              keyPath: 'id', 
              autoIncrement: withAutomaticId 
            });
          }
        };
        
        request.onsuccess = () => {
          this.db = request.result;
          resolve(null);
        };
        
        request.onerror = () => {
          reject(new Error(`Failed to create table ${tableName}`));
        };
      });
    }
    
    return Promise.resolve(null);
  }

  async createIndex(tableName: string, columnList: string[]): Promise<any> {
    // Gli indici vengono creati durante la creazione della tabella
    return Promise.resolve(null);
  }

  async createUniqueIndex(tableName: string, columnList: string[]): Promise<any> {
    // Gli indici vengono creati durante la creazione della tabella
    return Promise.resolve(null);
  }

  async addColumn(tableName: string, columnName: string, columnType: string): Promise<any> {
    // In IndexedDB, non possiamo aggiungere colonne dinamicamente come in SQL
    // Le nuove proprietà vengono semplicemente aggiunte agli oggetti quando necessario
    // Questo è un no-op per IndexedDB, ma manteniamo la compatibilità
    return Promise.resolve(null);
  }

  async clearTable(tableName: string): Promise<any> {
    
    const db = await this.openDatabase();
    
    return new Promise((resolve, reject) => {
      const transaction = db.transaction([tableName], 'readwrite');
      const store = transaction.objectStore(tableName);
      const request = store.clear();
      
      request.onsuccess = () => {
        resolve(null);
      };
      
      request.onerror = () => {
        reject(new Error(`Failed to clear table ${tableName}`));
      };
    });
  }

  async addRecord(tableName: string, columnList: string[], valueList: any[]): Promise<any> {
    const db = await this.openDatabase();

    // Crea un oggetto dai nomi delle colonne e valori
    const record: any = {};
    columnList.forEach((column, index) => {
      record[column] = valueList[index];
    });

    // Assicurati che ci sia sempre un id valido
    if (!record.id || record.id === null || record.id === undefined) {
      record.id = `temp_${tableName}_${Date.now()}_${Math.random()}`;
      console.warn(`⚠️ IndexedDB: Generated temporary id for addRecord in ${tableName}:`, record.id);
    }

    return new Promise((resolve, reject) => {
      const transaction = db.transaction([tableName], 'readwrite');
      const store = transaction.objectStore(tableName);
      const request = store.add(record);
      
      request.onsuccess = () => {
        resolve(null);
      };
      
      request.onerror = () => {
        reject(new Error(`Failed to add record to ${tableName}`));
      };
    });
  }

  async addRecords(tableName: string, columns: string[], records: any[][]): Promise<any> {
    
    const db = await this.openDatabase();
    
    return new Promise((resolve, reject) => {
      const transaction = db.transaction([tableName], 'readwrite');
      const store = transaction.objectStore(tableName);
      
      let completed = 0;
      let hasError = false;
      
      records.forEach((record, recordIndex) => {
        const recordObj: any = {};
        columns.forEach((column, index) => {
          recordObj[column] = record[index];
        });

        // Assicurati che ci sia sempre un id valido
        if (!recordObj.id || recordObj.id === null || recordObj.id === undefined) {
          recordObj.id = `temp_${tableName}_${Date.now()}_${recordIndex}`;
          console.warn(`⚠️ IndexedDB: Generated temporary id for addRecords in ${tableName}:`, recordObj.id);
        }

        const request = store.add(recordObj);
        
        request.onsuccess = () => {
          completed++;
          if (completed === records.length && !hasError) {
            resolve(null);
          }
        };
        
        request.onerror = () => {
          if (!hasError) {
            hasError = true;
            reject(new Error(`Failed to add records to ${tableName}`));
          }
        };
      });
      
      if (records.length === 0) {
        resolve(null);
      }
    });
  }

  async replaceIntoRecord(tableName: string, columnList: string[], valueList: any[]): Promise<any> {
    const db = await this.openDatabase();

    const record: any = {};
    columnList.forEach((column, index) => {
      record[column] = valueList[index];
    });

    // Assicurati che ci sia sempre un id valido
    if (!record.id || record.id === null || record.id === undefined) {
      record.id = `temp_${tableName}_${Date.now()}_${Math.random()}`;
      console.warn(`⚠️ IndexedDB: Generated temporary id for replaceIntoRecord in ${tableName}:`, record.id);
    }

    return new Promise((resolve, reject) => {
      const transaction = db.transaction([tableName], 'readwrite');
      const store = transaction.objectStore(tableName);
      const request = store.put(record);

      request.onsuccess = () => {
        resolve(null);
      };

      request.onerror = () => {
        reject(new Error(`Failed to replace record in ${tableName}`));
      };
    });
  }

  async multipleInsertOrReplaceByTransaction(
    tableName: string,
    isIdAutoincrement: boolean,
    columns: string[],
    records: { andConditionList: { key: string, value: any }[]; values: any[] }[]
  ): Promise<any> {

    const db = await this.openDatabase();

    return new Promise((resolve, reject) => {
      const transaction = db.transaction([tableName], 'readwrite');
      const store = transaction.objectStore(tableName);

      let completed = 0;
      let hasError = false;

      records.forEach((record, recordIndex) => {
        const recordObj: any = {};

        // Assicurati che ci sia sempre un id valido
        const idCondition = record.andConditionList.find(cond => cond.key === 'id');
        if (idCondition && idCondition.value !== null && idCondition.value !== undefined) {
          recordObj.id = idCondition.value;
        } else {
          // Se non c'è un id valido nelle condizioni, cerca nelle colonne
          const idColumnIndex = columns.findIndex(col => col === 'id');
          if (idColumnIndex >= 0 && record.values[idColumnIndex] !== null && record.values[idColumnIndex] !== undefined) {
            recordObj.id = record.values[idColumnIndex];
          } else {
            // Come ultima risorsa, genera un id temporaneo
            recordObj.id = `temp_${tableName}_${Date.now()}_${recordIndex}`;
            console.warn(`⚠️ IndexedDB: Generated temporary id for record in ${tableName}:`, recordObj.id);
          }
        }

        // Aggiungi tutti i valori
        columns.forEach((column, index) => {
          if (column !== 'id') {
            recordObj[column] = record.values[index];
          }
        });

        const request = store.put(recordObj);

        request.onsuccess = () => {
          completed++;
          if (completed === records.length && !hasError) {
            resolve(null);
          }
        };

        request.onerror = () => {
          if (!hasError) {
            hasError = true;
            reject(new Error(`Failed to process records in ${tableName}`));
          }
        };
      });

      if (records.length === 0) {
        resolve(null);
      }
    });
  }

  async getAll(tableName: string[], columnList?: string[], joinList?: string[]): Promise<any[]> {

    const db = await this.openDatabase();
    const mainTable = tableName[0];

    return new Promise((resolve, reject) => {
      const transaction = db.transaction([mainTable], 'readonly');
      const store = transaction.objectStore(mainTable);
      const request = store.getAll();

      request.onsuccess = () => {
        let results = request.result;

        // Se sono specificate colonne, filtra solo quelle
        if (columnList && columnList.length > 0) {
          results = results.map(record => {
            const filtered: any = {};
            columnList.forEach(col => {
              if (record.hasOwnProperty(col)) {
                filtered[col] = record[col];
              }
            });
            return filtered;
          });
        }
        resolve(results);
      };

      request.onerror = () => {
        reject(new Error(`Failed to get records from ${mainTable}`));
      };
    });
  }

  async getRecordsByANDCondition(tableName: string, conditionalValueList: {key: string, value: string}[]): Promise<any[]> {

    const db = await this.openDatabase();

    return new Promise((resolve, reject) => {
      const transaction = db.transaction([tableName], 'readonly');
      const store = transaction.objectStore(tableName);
      const request = store.getAll();

      request.onsuccess = () => {
        let results = request.result;

        // Filtra i risultati in base alle condizioni AND
        results = results.filter(record => {
          return conditionalValueList.every(condition => {
            return record[condition.key] && record[condition.key].toString() === condition.value;
          });
        });

        resolve(results);
      };

      request.onerror = () => {
        reject(new Error(`Failed to get records from ${tableName}`));
      };
    });
  }

  async deleteRecord(tableName: string, andConditionList: {key: string, value: any}[]): Promise<any> {

    const db = await this.openDatabase();

    return new Promise((resolve, reject) => {
      const transaction = db.transaction([tableName], 'readwrite');
      const store = transaction.objectStore(tableName);

      // Se c'è una condizione con id, usa quella per la cancellazione diretta
      const idCondition = andConditionList.find(cond => cond.key === 'id');

      if (idCondition) {
        const request = store.delete(idCondition.value);

        request.onsuccess = () => {
          resolve(null);
        };

        request.onerror = () => {
          reject(new Error(`Failed to delete record from ${tableName}`));
        };
      } else {
        // Se non c'è id, cerca tutti i record e cancella quelli che corrispondono
        const getAllRequest = store.getAll();

        getAllRequest.onsuccess = () => {
          const records = getAllRequest.result;
          const toDelete = records.filter(record => {
            return andConditionList.every(condition => {
              return record[condition.key] && record[condition.key].toString() === condition.value.toString();
            });
          });

          let deleted = 0;
          toDelete.forEach(record => {
            const deleteRequest = store.delete(record.id);
            deleteRequest.onsuccess = () => {
              deleted++;
              if (deleted === toDelete.length) {
                resolve(null);
              }
            };
          });

          if (toDelete.length === 0) {
            resolve(null);
          }
        };

        getAllRequest.onerror = () => {
          reject(new Error(`Failed to find records to delete from ${tableName}`));
        };
      }
    });
  }

  async insertOrReplace(tableName: string, andConditionList: {key: string, value: any}[], columnList: string[], valueList: any[]): Promise<any> {
    const db = await this.openDatabase();

    const record: any = {};
    columnList.forEach((column, index) => {
      record[column] = valueList[index];
    });

    // Trova l'ID dalle condizioni o dalle colonne
    const idCondition = andConditionList.find(cond => cond.key === 'id');
    if (idCondition && idCondition.value !== null && idCondition.value !== undefined) {
      record.id = idCondition.value;
    } else {
      const idColumnIndex = columnList.findIndex(col => col === 'id');
      if (idColumnIndex >= 0 && valueList[idColumnIndex] !== null && valueList[idColumnIndex] !== undefined) {
        record.id = valueList[idColumnIndex];
      } else {
        record.id = `temp_${tableName}_${Date.now()}_${Math.random()}`;
        console.warn(`⚠️ IndexedDB: Generated temporary id for insertOrReplace in ${tableName}:`, record.id);
      }
    }

    return new Promise((resolve, reject) => {
      const transaction = db.transaction([tableName], 'readwrite');
      const store = transaction.objectStore(tableName);
      const request = store.put(record);

      request.onsuccess = () => {
        resolve(null);
      };

      request.onerror = () => {
        reject(new Error(`Failed to insert or replace record in ${tableName}`));
      };
    });
  }

  async insertOrReplaceWithoutID(tableName: string, andConditionList: {key: string, value: any}[], columnList: string[], valueList: any[]): Promise<any> {
    const db = await this.openDatabase();

    const record: any = {};
    columnList.forEach((column, index) => {
      record[column] = valueList[index];
    });

    // Per questo metodo, generiamo sempre un ID se non presente
    if (!record.id || record.id === null || record.id === undefined) {
      record.id = `temp_${tableName}_${Date.now()}_${Math.random()}`;
    }

    return new Promise((resolve, reject) => {
      const transaction = db.transaction([tableName], 'readwrite');
      const store = transaction.objectStore(tableName);
      const request = store.put(record);

      request.onsuccess = () => {
        resolve(null);
      };

      request.onerror = () => {
        reject(new Error(`Failed to insert or replace record without ID in ${tableName}`));
      };
    });
  }

  async getDistinct(tableName: string, columnList?: string[]): Promise<any[]> {
    const db = await this.openDatabase();

    return new Promise((resolve, reject) => {
      const transaction = db.transaction([tableName], 'readonly');
      const store = transaction.objectStore(tableName);
      const request = store.getAll();

      request.onsuccess = () => {
        let results = request.result;

        // Se sono specificate colonne, filtra solo quelle e rimuovi duplicati
        if (columnList && columnList.length > 0) {
          const seen = new Set();
          results = results.filter(record => {
            const key = columnList.map(col => record[col]).join('|');
            if (seen.has(key)) {
              return false;
            }
            seen.add(key);
            return true;
          }).map(record => {
            const filtered: any = {};
            columnList.forEach(col => {
              if (record.hasOwnProperty(col)) {
                filtered[col] = record[col];
              }
            });
            return filtered;
          });
        } else {
          // Rimuovi duplicati completi
          const seen = new Set();
          results = results.filter(record => {
            const key = JSON.stringify(record);
            if (seen.has(key)) {
              return false;
            }
            seen.add(key);
            return true;
          });
        }

        resolve(results);
      };

      request.onerror = () => {
        reject(new Error(`Failed to get distinct records from ${tableName}`));
      };
    });
  }

  async getAllGroupedBy(tableName: string, columnList?: string[], groupBy?: string[]): Promise<any[]> {
    const db = await this.openDatabase();

    return new Promise((resolve, reject) => {
      const transaction = db.transaction([tableName], 'readonly');
      const store = transaction.objectStore(tableName);
      const request = store.getAll();

      request.onsuccess = () => {
        let results = request.result;

        if (!groupBy || groupBy.length === 0) {
          // Se sono specificate colonne, filtra solo quelle
          if (columnList && columnList.length > 0) {
            results = results.map(record => {
              const filtered: any = {};
              columnList.forEach(col => {
                if (record.hasOwnProperty(col)) {
                  filtered[col] = record[col];
                }
              });
              return filtered;
            });
          }
          resolve(results);
          return;
        }

        // Raggruppa manualmente
        const groups = new Map();
        results.forEach(record => {
          const key = groupBy.map(col => record[col]).join('|');
          if (!groups.has(key)) {
            if (columnList && columnList.length > 0) {
              const filtered: any = {};
              columnList.forEach(col => {
                if (record.hasOwnProperty(col)) {
                  filtered[col] = record[col];
                }
              });
              groups.set(key, filtered);
            } else {
              groups.set(key, record);
            }
          }
        });

        resolve(Array.from(groups.values()));
      };

      request.onerror = () => {
        reject(new Error(`Failed to get grouped records from ${tableName}`));
      };
    });
  }

  async getColumnFromTableByANDCondition(tableName: string, columnList: string[], conditionalValueList: {key: string, value: string}[]): Promise<any[]> {
    const records = await this.getRecordsByANDCondition(tableName, conditionalValueList);

    // Filtra solo le colonne richieste e rimuovi duplicati
    const seen = new Set();
    return records.filter(record => {
      const key = columnList.map(col => record[col]).join('|');
      if (seen.has(key)) {
        return false;
      }
      seen.add(key);
      return true;
    }).map(record => {
      const filtered: any = {};
      columnList.forEach(col => {
        if (record.hasOwnProperty(col)) {
          filtered[col] = record[col];
        }
      });
      return filtered;
    });
  }

  async getColumnFromTableByORCondition(tableName: string, columnList: string[], conditionalValueList: {key: string, value: string}[]): Promise<any[]> {
    const db = await this.openDatabase();

    return new Promise((resolve, reject) => {
      const transaction = db.transaction([tableName], 'readonly');
      const store = transaction.objectStore(tableName);
      const request = store.getAll();

      request.onsuccess = () => {
        let results = request.result;

        // Filtra con condizioni OR
        results = results.filter(record => {
          return conditionalValueList.some(condition => {
            return record[condition.key] && record[condition.key].toString() === condition.value;
          });
        });

        // Filtra solo le colonne richieste e rimuovi duplicati
        const seen = new Set();
        results = results.filter(record => {
          const key = columnList.map(col => record[col]).join('|');
          if (seen.has(key)) {
            return false;
          }
          seen.add(key);
          return true;
        }).map(record => {
          const filtered: any = {};
          columnList.forEach(col => {
            if (record.hasOwnProperty(col)) {
              filtered[col] = record[col];
            }
          });
          return filtered;
        });

        resolve(results);
      };

      request.onerror = () => {
        reject(new Error(`Failed to get records from ${tableName}`));
      };
    });
  }

  async getRecordsByINCondition(tableName: string, conditionalValueList: {key: string, value: string[]}): Promise<any[]> {
    const db = await this.openDatabase();

    return new Promise((resolve, reject) => {
      const transaction = db.transaction([tableName], 'readonly');
      const store = transaction.objectStore(tableName);
      const request = store.getAll();

      request.onsuccess = () => {
        const results = request.result;

        // Filtra con condizione IN
        const filtered = results.filter(record => {
          return conditionalValueList.value.includes(record[conditionalValueList.key]);
        });

        resolve(filtered);
      };

      request.onerror = () => {
        reject(new Error(`Failed to get records from ${tableName}`));
      };
    });
  }

  async updateRecord(tableName: string, andConditionList: {key: string, value: any}[], columnList: {key: string, value: any}[]): Promise<any> {
    const db = await this.openDatabase();

    return new Promise((resolve, reject) => {
      const transaction = db.transaction([tableName], 'readwrite');
      const store = transaction.objectStore(tableName);

      // Prima ottieni i record che corrispondono alle condizioni
      const getAllRequest = store.getAll();

      getAllRequest.onsuccess = () => {
        const records = getAllRequest.result;
        const toUpdate = records.filter(record => {
          return andConditionList.every(condition => {
            return record[condition.key] && record[condition.key].toString() === condition.value.toString();
          });
        });

        if (toUpdate.length === 0) {
          resolve(null);
          return;
        }

        let updated = 0;
        toUpdate.forEach(record => {
          // Aggiorna le colonne specificate
          columnList.forEach(col => {
            record[col.key] = col.value;
          });

          const updateRequest = store.put(record);
          updateRequest.onsuccess = () => {
            updated++;
            if (updated === toUpdate.length) {
              resolve(null);
            }
          };

          updateRequest.onerror = () => {
            reject(new Error(`Failed to update record in ${tableName}`));
          };
        });
      };

      getAllRequest.onerror = () => {
        reject(new Error(`Failed to find records to update in ${tableName}`));
      };
    });
  }
}
