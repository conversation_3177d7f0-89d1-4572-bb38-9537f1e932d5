import { Injectable } from '@angular/core';
import { Platform } from '@ionic/angular';
import { CapacitorSQLite, SQLiteConnection, SQLiteDBConnection } from '@capacitor-community/sqlite';
import { TableProperty } from 'src/app/shared/table-property';
import customersJson from 'src/mock/customers.json';
import cartsJson from 'src/mock/carts.json';
import categoriesJson from 'src/mock/categories.json';
import productsJson from 'src/mock/products.json'
import prospectTypesJson from 'src/mock/prospectTypes.json'
import favoritesJson from 'src/mock/favorites.json'
import discountsJson from 'src/mock/discounts.json'
import datacolCategoriesJson from 'src/mock/datacolCategories.json'
import productsCountJson from 'src/mock/productsCount.json'
import subcategoriesJson from 'src/mock/subcategories.json'

@Injectable({
  providedIn: 'root'
})
export class DbService {
  private sqlite: SQLiteConnection;
  private db: SQLiteDBConnection;

  constructor(private platform: Platform) {
    this.sqlite = new SQLiteConnection(CapacitorSQLite);

    console.log("Using DEV DB");
  }

  private isNumeric = (val: string) : boolean => {
    return !isNaN(Number(val));
  }


  async createTable(tableName: string, columnList: TableProperty[], witAutomaticId: boolean = false) : Promise<any> {
    const query = `CREATE TABLE IF NOT EXISTS ${tableName}
      ( ${witAutomaticId ? 'id INTEGER PRIMARY KEY AUTOINCREMENT,' : ''}
      ${columnList.map(x => (Object.keys(x).map((key) => x[key]).toString().replace(',', ' ') + (!witAutomaticId && x.columnName === 'id' ? ' PRIMARY KEY ':''))).join(', ')})`;
      const db = await this.sqlite.createConnection('datacol_db', false, 'no-encryption', 1, false);
      await this.platform.ready();
      if(this.platform.is('capacitor'))
      {
        await db.open();
        await db.execute(query, false);
      }
      return new Promise((resolve) => { resolve(null); });
  }

  async createUniqueIndex(tableName: string, columnList: string[]) : Promise<any> {
    try {
      return await this.createUniqueIndexWithException(tableName, columnList);
    } catch (error) {
      console.log(`createUniqueIndex ${tableName} error: `, error);
    }
    return new Promise((resolve) => { resolve(null); });
  }

  async createUniqueIndexWithException(tableName: string, columnList: string[]) : Promise<any> {
    const query = `CREATE UNIQUE INDEX IF NOT EXISTS idx_${tableName}
      ON ${tableName} (${columnList.join(', ')})`;
    const db = await this.sqlite.createConnection('datacol_db', false, 'no-encryption', 1, false);
    await this.platform.ready();
    if(this.platform.is('capacitor'))
    {
    await db.execute(query);
    }
    return new Promise((resolve) => { resolve(null); });
  }

  async createIndex(tableName: string, columnList: string[]) : Promise<any> {
    const query = `CREATE INDEX IF NOT EXISTS idx_${tableName}_${columnList[0]}
                  ON ${tableName} (${columnList.join(', ')})`;
      const db = await this.sqlite.createConnection('datacol_db', false, 'no-encryption', 1, false);
      await this.platform.ready();
      if(this.platform.is('capacitor'))
      {
        await db.execute(query);
      }
      return new Promise((resolve) => { resolve(null); });
  }

  async addColumn(tableName: string, columnName: string, columnType: string) {
    try {
      const query = `ALTER TABLE ${tableName} ADD COLUMN ${columnName} ${columnType}`;
      const db = await this.sqlite.createConnection('datacol_db', false, 'no-encryption', 1, false);
      await this.platform.ready();
      if(this.platform.is('capacitor'))
      {
        await db.execute(query);
      }
    }catch (error) {
      console.log(`addColumn ${columnName} to table ${tableName} error: `, error);
    }
    return new Promise((resolve) => { resolve(null); });
  }

  async clearTable(tableName: string) : Promise<any> {
      const db = await this.sqlite.createConnection('datacol_db', false, 'no-encryption', 1, false);
      await this.platform.ready();
      if(this.platform.is('capacitor'))
      {
        db.execute(`DELETE FROM ${tableName}`);
        db.execute(`DELETE FROM SQLITE_SEQUENCE WHERE name='${tableName}'`);
      }
  }

  async addRecord(tableName: string, columnList: string[], valueList: any[]) : Promise<any> {
    await this.platform.ready();
    if(this.platform.is('capacitor'))
    {
        const db = await this.sqlite.createConnection('datacol_db', false, 'no-encryption', 1, false);
        const query = `INSERT INTO ${tableName} (${columnList.join(', ')})
        VALUES (${Array(columnList.length).fill('?').join(', ')})`;
        return db.execute(query, false);
    }
    return new Promise((resolve) => { resolve(null); });
  }

  async addRecords(tableName: string, columns: string[], records: any[][]) : Promise<any> {
    await this.platform.ready();
    if(this.platform.is('capacitor'))
    {
        const db = await this.sqlite.createConnection('datacol_db', false, 'no-encryption', 1, false);
        let _this = this;
          return new Promise<void>(async resolve => {
            for (const record of records) {
              const query = `INSERT INTO ${tableName} (${columns.join(', ')})
              VALUES (${Array(columns.length).fill('?').join(', ')})`;
              try {
                await db.execute(query, false);
              } catch (error) {
                console.log("Error executing query:", error);
                console.log("Record:", record);
              }
            }
            resolve();
          });
    }
    return new Promise((resolve) => { resolve(null); });
  }

  async replaceIntoRecord(tableName: string, columnList: string[], valueList: any[]) : Promise<any>{
    await this.platform.ready();
    if(this.platform.is('capacitor'))
    {
        const db = await this.sqlite.createConnection('datacol_db', false, 'no-encryption', 1, false);
        const query = `REPLACE INTO ${tableName} (${columnList.join(', ')})
        VALUES (${Array(columnList.length).fill('?').join(', ')})`;
        return db.execute(query, false);
    }
    return new Promise((resolve) => { resolve(null); });
  }

  async multipleInsertOrReplaceByTransaction(tableName: string, isIdAuthoincrement:boolean, columns: string[], records: {andConditionList: {key: string, value: any}[], values: any[]}[]){
    await this.platform.ready();
    if(this.platform.is('capacitor'))
    {
      const db = await this.sqlite.createConnection('datacol_db', false, 'no-encryption', 1, false);
      return new Promise<void>(async resolve => {
        for (const record of records) {
          if(isIdAuthoincrement)
          {
            const query = `INSERT OR REPLACE INTO ${tableName} (${columns.join(', ')})
            VALUES ((SELECT ID FROM ${tableName} WHERE ${record.andConditionList.map(({key, value}) => `${key} = '${value}'` ).join(' AND ')}),
            ${record.values.map((date) => this.isNumeric(date) ? `'${date.toString()}'` : `'${date.replaceAll('\'', '&apos;')}'`)})`;
            await db.execute(query);
          } else if (record.andConditionList.length === 1 && record.andConditionList[0].key === 'id'){
            const query = `INSERT OR REPLACE INTO ${tableName} (${columns.join(', ')})
            VALUES ( COALESCE((SELECT ID FROM ${tableName} WHERE id = ${record.andConditionList[0].value}), ${record.andConditionList[0].value} ),
            ${record.values.map((date) => this.isNumeric(date) ? `'${date.toString()}'` : `'${date.replaceAll('\'', '&apos;')}'`)})`;

            await db.execute(query);
          } else
            console.error('chiavi da controllare');
        }
        resolve();
      });
    }
    return new Promise((resolve) => { resolve(null); });
  }

  async insertOrReplace(tableName: string, andConditionList: {key: string, value: any}[], columnList: string[], valueList: any[]){
    await this.platform.ready();
    if(this.platform.is('capacitor'))
    {
      const db = await this.sqlite.createConnection('datacol_db', false, 'no-encryption', 1, false);
      const query = `INSERT OR REPLACE INTO ${tableName} (${columnList.join(', ')})
      VALUES ((SELECT ID FROM ${tableName} WHERE ${andConditionList.map(({key, value}) => `${key} = '${value}'` ).join(' AND ')}),
      ${valueList.map((date) => this.isNumeric(date) ? `'${date.toString()}'` : `'${date.replaceAll('\'', '&apos;')}'`)})`;
      return db.execute(query);
    }
    return new Promise((resolve) => { resolve(null); });
  }

  async insertOrReplaceWithoutID(tableName: string, andConditionList: {key: string, value: any}[], columnList: string[], valueList: any[]){
    await this.platform.ready();
    if(this.platform.is('capacitor'))
    {
      const db = await this.sqlite.createConnection('datacol_db', false, 'no-encryption', 1, false);
      const query = `INSERT OR REPLACE INTO ${tableName} (${columnList.join(', ')})
      VALUES (${valueList.map((date) => this.isNumeric(date) ? `'${date.toString()}'` : `'${date.replaceAll('\'', '&apos;')}'`)})`;
      return db.execute(query);
    }
    return new Promise((resolve) => { resolve(null); });
  }

  async getDistinct(tableName: string, columnList?: string[]): Promise<any[]> {
    let items: any[] = [];
    await this.platform.ready();
    if(this.platform.is('capacitor'))
    {
      const db = await this.sqlite.createConnection('datacol_db', false, 'no-encryption', 1, false);
      const query = `SELECT DISTINCT ${columnList.length > 0 ? columnList.map(x => x).join(', ') : '*'} FROM ${tableName}`;
      const res = await db.query(query);
        if (res.values.length > 0) {
          for (let i = 0; i < res.values.length; i++) {
            items.push(res.values[i]);
          }
        }
      return items;
    } 
    else {
       return customersJson.map(({name, uid, industrialSector}) => ({name, uid, industrialSector}));
     }
  }

  async getAll(tableName: string[], columnList?: string[], joinList?: string[]): Promise<any[]> {
    let items: any[] = [];
    await this.platform.ready();
    if(this.platform.is('capacitor'))
    {
      const db = await this.sqlite.createConnection('datacol_db', false, 'no-encryption', 1, false);
      const query = `SELECT ${columnList.length > 0 ? columnList.map(x => x).join(', ') : '*'} FROM ${tableName.map(x => x).join(' INNER JOIN ')}
      ${typeof joinList === "undefined" || joinList.length === 0 ? '': `ON ${joinList.map(x => x).join(' AND ')}`}`;
      const res = await db.query(query);
      if (!!res.values && res.values.length > 0) {
        for (let i = 0; i < res.values.length; i++) {
          items.push(res.values[i]);
        }
      }
      return new Promise((resolve) => { resolve(items); });
    } 
    else {
      if(tableName[0] === 'customers')
        return customersJson.map(({name, uid, industrialSector, customerStatus, infoDataList}) => ({name, uid, industrialSector, customerStatus, infoDataList}));
      else if(tableName.includes("carts") || tableName.includes("carts CA"))
        return cartsJson;
      else if(tableName[0] === "categories")
        return categoriesJson as any[];
      else if(tableName[0] === "prospectTypes")
        return prospectTypesJson;
      else if(tableName[0] === "discounts")
        return discountsJson;
      else if(tableName[0] === "products")
        return productsJson;
      else if (tableName[0] === 'datacolCategories')
        return datacolCategoriesJson;
      else if (tableName[0] === 'favorites')
        return favoritesJson;
        else if (tableName[0] === 'subcategories')
        return subcategoriesJson;
    }
  }

  async getAllGroupedBy(tableName: string, columnList?: string[], groupBy?: string[]): Promise<any[]> {
    let items: any[] = [];
    await this.platform.ready();
    if(this.platform.is('capacitor'))
    {
      const db = await this.sqlite.createConnection('datacol_db', false, 'no-encryption', 1, false);
      const query = `SELECT ${columnList.length > 0 ? columnList.map(x => x).join(', ') : '*'} FROM ${tableName}
      GROUP BY ${groupBy.map(x => x).join(', ')} `;
      const res = await db.query(query);
      if (res.values.length > 0) {
        for (let i = 0; i < res.values.length; i++) {
          items.push(res.values[i]);
        }
      }
      return new Promise((resolve) => { resolve(items); });
    } 
    else {
      return productsCountJson;
    }
  }

  async getRecordsByANDCondition(tableName: string, conditionalValueList: {key: string, value: string}[]): Promise<any> {
    let items: any[] = [];
    await this.platform.ready();
    if(this.platform.is('capacitor'))
    {
      const db = await this.sqlite.createConnection('datacol_db', false, 'no-encryption', 1, false);
      const query = `SELECT * FROM ${tableName} WHERE ${ conditionalValueList.map(({key, value}) =>  `${key} = '${value}'` ).join(' AND ')} `;
      const res = await db.query(query);

      if (res.values.length > 0) {
        for (let i = 0; i < res.values.length; i++) {
          items.push(res.values[i]);
        }
      }

      return new Promise((resolve) => { resolve(items); });
    } 
else {
      if(tableName === 'customers')
        return customersJson.filter(x=> x.uid == conditionalValueList[0].value);
      else if(tableName === 'carts')
        return [cartsJson[0]]; // cartsJson
      else if(tableName === 'products')
        return productsJson; // [productsJson[0]]; 
      else if(tableName === 'favorites')
        return favoritesJson;
      else if(tableName === 'userSettings')
        return ['jk'];
      else if(tableName === 'discounts')
        return discountsJson;
      else if(tableName === 'subcategories')
      return subcategoriesJson;
    } 
  }

  async getColumnFromTableByANDCondition(tableName: string, columnList: string[], conditionalValueList: {key: string, value: string}[]): Promise<any> {
    let items: any[] = [];
    await this.platform.ready();
    if(this.platform.is('capacitor'))
    {
      const db = await this.sqlite.createConnection('datacol_db', false, 'no-encryption', 1, false);
      const query = `SELECT DISTINCT ${columnList.length > 0 ? columnList.map(x => x).join(', ') : '*'} FROM ${tableName} WHERE ${ conditionalValueList.map(({key, value}) =>  `${key} = '${value}'` ).join(' AND ')} `;
      const res = await db.query(query);
      if (res.values.length > 0) {
        for (let i = 0; i < res.values.length; i++) {
          items.push(res.values[i]);
        }
      }
      return new Promise((resolve) => { resolve(items); });
    } 
    else {
      if(tableName === 'products')
        return productsJson.filter(x=> x.focus === 'S');
    }
  }

  async getColumnFromTableByORCondition(tableName: string, columnList: string[], conditionalValueList: {key: string, value: string}[]): Promise<any> {
    let items: any[] = [];
    await this.platform.ready();
    if(this.platform.is('capacitor'))
    {
      const db = await this.sqlite.createConnection('datacol_db', false, 'no-encryption', 1, false);
      const query = `SELECT DISTINCT ${columnList.length > 0 ? columnList.map(x => x).join(', ') : '*'} FROM ${tableName} WHERE ${ conditionalValueList.map(({key, value}) =>  `${key} = '${value}'` ).join(' OR ')} `;
      const res = await db.query(query);
      if (res.values.length > 0) {
        for (let i = 0; i < res.values.length; i++) {
          items.push(res.values[i]);
        }
      }
      return new Promise((resolve) => { resolve(items); });
    } 
    else {
      if(tableName === 'products')
        return productsJson.filter(x=> x.focus === 'S');
    }
  }

  async getRecordsByINCondition(tableName: string, conditionalValueList: {key: string, value: string[]}): Promise<any> {
    let items: any[] = [];
    await this.platform.ready();
    if(this.platform.is('capacitor'))
    {
      const db = await this.sqlite.createConnection('datacol_db', false, 'no-encryption', 1, false);
      const query = `SELECT * FROM ${tableName} WHERE ${ conditionalValueList.key } IN ( ${ conditionalValueList.value.map((value) =>  `'${value}'` ).join(',')}) `;
      const res = await db.query(query);
      if (res.values.length > 0) {
        for (let i = 0; i < res.values.length; i++) {
          items.push(res.values[i]);
        }
      }
      return new Promise((resolve) => { resolve(items); });
    } 
    else {
      if(tableName === 'products')
        return productsJson;
    }
  }

  async deleteRecord(tableName: string, andConditionList: {key: string, value: any}[]) : Promise<any>{
    await this.platform.ready();
    if(this.platform.is('capacitor'))
    {
        const db = await this.sqlite.createConnection('datacol_db', false, 'no-encryption', 1, false);
        const query = `DELETE FROM ${tableName} WHERE ${andConditionList.map(({key, value}) => key + ' = ' + value ).join(' AND ')}`;
        return db.execute(query);
    }
    return new Promise((resolve) => { resolve(null); });
  }

  async updateRecord(tableName: string, andConditionList: {key: string, value: any}[], columnList: {key: string, value: any}[]) {
    const query = `UPDATE ${tableName} SET
    ${columnList.map(({key, value}) => `${key} = '${value}'` ).join(', ')}
    WHERE ${andConditionList.map(({key, value}) => key + ' = ' + value ).join(' AND ')}`;
    await this.platform.ready();
    if(this.platform.is('capacitor'))
    {
        const db = await this.sqlite.createConnection('datacol_db', false, 'no-encryption', 1, false);

        return db.execute(query);
    }
    return new Promise((resolve) => { resolve(null); });
  }
}
