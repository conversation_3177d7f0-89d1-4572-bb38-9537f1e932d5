import { CommonModule } from '@angular/common';
import { Component, EventEmitter, inject, OnInit, Output } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { IonicModule, LoadingController } from '@ionic/angular';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { ProspectType } from 'src/app/service/data/prospect-type';
import { DbService } from 'src/app/shared/db.service';
import { addIcons } from 'ionicons';
import { closeOutline } from 'ionicons/icons';
import { NavigationService } from 'src/app/service/navigation/navigation.service';
import { CatalogService } from 'src/app/service/catalog/catalog.service';

@Component({
    selector: 'app-prospect-types',
    templateUrl: './prospect-types.component.html',
    styleUrls: ['./prospect-types.component.scss'],
    standalone: true,
    imports: [
      CommonModule,
      IonicModule,
      FormsModule,
      ReactiveFormsModule,
      TranslateModule
    ]
})
export class ProspectTypesComponent implements OnInit {

  catalogService = inject(CatalogService);
  navigationService = inject(NavigationService);
  @Output() closeProspectTypes = new EventEmitter();
  prospectTypes: ProspectType[] = [];

  constructor(private _router: Router, private _loadingController: LoadingController, private _translate: TranslateService,
    private _dbService: DbService) { 
      addIcons({ closeOutline });
    }

  ngOnInit() {
    this.getAllData();
  }

  private async getAllData(){
    const loading = await this._loadingController.create({
      message: this._translate.instant('GENERICS.WAIT')
    });
    loading.present();

    const _this = this;
    await this._dbService.getAll(["prospectTypes"], ['uid', 'name', 'catalog']).then((data) => {
      _this.prospectTypes = data.map( (item:ProspectType)=>{
        return item as unknown as ProspectType
      });
    }).finally(async () => {
      loading.dismiss();
    });
  }

  close() {
    this.closeProspectTypes.emit();
  }

  startCustomerSession(item: ProspectType) {

    // Qui parte la navigazione al catalogo dopo aver selezionato un prospect
      if(!!item) {
        this.catalogService.setIsProspect(true);
        this.catalogService.setCustomerUid(item.uid);
        this.catalogService.setCustomerName(item.name);
        this.catalogService.setShowFavorites(false);
        this.catalogService.setRootView(true);
        this.navigationService.navigateTo('/private/catalog');
      }
        
  }
}
