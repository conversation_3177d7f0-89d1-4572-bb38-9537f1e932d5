import { Component, EventEmitter, inject, Input, OnInit, Output } from '@angular/core';
import { FormsModule, ReactiveFormsModule, UntypedFormControl } from '@angular/forms';
import { Router } from '@angular/router';
import { Customer } from 'src/app/service/data/customer';
import { CustomerCard } from 'src/app/service/data/customer-card';
import { HybridDbService } from 'src/app/shared/hybrid-db.service';
import Utils from 'src/app/shared/utils';
import { IonicModule, LoadingController, Platform } from '@ionic/angular';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { Product, ProductSearchItem } from 'src/app/service/data/product';
import { Store, select } from '@ngrx/store';
import { setCurrentCustomer } from 'src/app/store/actions/current-customer.actions';
import { setProducts } from 'src/app/store/actions/products.actions';
import { map, startWith } from 'rxjs';
import { CommonModule } from '@angular/common';
import { ZeroRemoverPipe } from 'src/app/shared/zero-remover.pipe';
import { SafePipe } from 'src/app/shared/safe-pipe';
import { addIcons } from 'ionicons';
import { closeOutline } from 'ionicons/icons';
import { NavigationService } from 'src/app/service/navigation/navigation.service';
import { CatalogService } from 'src/app/service/catalog/catalog.service';
@Component({
    selector: 'app-search',
    templateUrl: './search.component.html',
    styleUrls: ['./search.component.scss'],
    standalone: true,
    imports: [
      CommonModule,
      IonicModule,
      FormsModule,
      ReactiveFormsModule,
      TranslateModule,
      ZeroRemoverPipe,
      SafePipe
    ]
})
export class SearchComponent implements OnInit {

  navigationService = inject(NavigationService);
  catalogService = inject(CatalogService);
  @Input() viewType: 'customers' | 'catalog'
  @Input() simpleView: boolean = false;
  @Input() customStyle: 'minified' | null = null;
  @Output() closeSearch = new EventEmitter();
  public searchField: UntypedFormControl;
  items: CustomerCard[] | ProductSearchItem[] = [];
  filtered$;
  imageDirectory: string = localStorage.getItem("imageDirectory");

  constructor(private _platform: Platform, private _router: Router, private _loadingController: LoadingController, private _translate: TranslateService, private _dbService: HybridDbService,
    private _store: Store<any>) {
    this.searchField = new UntypedFormControl('');
    addIcons({ closeOutline });
  }

  async ngOnInit() {
    await this._platform.ready();
    const loading = await this._loadingController.create({
      message: this._translate.instant('GENERICS.WAIT'),
    });
    try {
      const searchTerm$ = this.searchField.valueChanges.pipe(
        startWith(this.searchField.value)
      );
      if (this.items.length === 0)
        await this.preloadData(loading);
      this.filtered$ = searchTerm$.pipe(
        map((searchTerm) => {
          const normalizedSearchTerm = this.normalizeString(searchTerm);
          const combinedSearchTerm = this.normalizeCodeString(searchTerm);

          if (this.viewType === 'customers') {
            return (this.items as CustomerCard[]).filter((item) => {
              const normalizedItemName = this.normalizeString(item.name);
              const normalizedItemUid = this.normalizeString(item.uid);

              return normalizedSearchTerm === '' ||
                normalizedItemName.includes(normalizedSearchTerm) ||
                normalizedItemUid.includes(normalizedSearchTerm);
            });
          } else if (searchTerm.length > 3) {
            return (this.items as Product[]).filter((item) => {
              const normalizedItemName = this.normalizeString(item.name);
              const combinedItemCode = this.normalizeCodeString(item.code);

              return combinedSearchTerm === '' ||
                normalizedItemName.includes(normalizedSearchTerm) ||
                combinedItemCode.includes(combinedSearchTerm);
            }).map(item => {
              let image = '';
              Utils.resolveImageUrl(this._platform, this.imageDirectory, item.image).then(result => { image = result });
              if (image.length > 0)
                Object.assign({}, item.image, { url: image });
              return item;
            });
          } else {
            return [];
          }
        })
      );
    } catch {
      loading.dismiss();
    }
  }

  private normalizeString(value: string): string {
    const normalizedValue = value.replace(/[^\w\s]|_/g, "").replace(/\s+/g, " ").toLowerCase();
    return normalizedValue;
  }

  private normalizeCodeString(value: string): string {
    const code = value.replace(/\s+/g, "").toUpperCase(); // Rimuove gli spazi e converte a maiuscolo
    return code;
  }

  async preloadData(loading: HTMLIonLoadingElement) {
    if (this.viewType === 'customers') {
      loading.present();
      await this._dbService.getAll(["customers"], ['name', 'uid', 'industrialSector']).then(async (data) => {
        // Trasformo in un elenco di oggetti CustomerCard
        this.items = data.map((item: Customer) => {
          return item as unknown as CustomerCard
        });
        // Metto tutto in ordine alfabetico
        this.items = await this.items.sort(function (a: CustomerCard, b: CustomerCard) {
          return Utils.compareStrings(a.name, b.name);
        });
        loading.dismiss();
      });
    } else if (this.viewType === 'catalog') {
      loading.present();
      this._store.pipe(select('products')).subscribe(res => this.items = [...res]).unsubscribe();
      if (this.items.length === 0) {
        await this._dbService.getAll(['products'], ['idSubCategory', 'code', 'image', 'name', 'minimumDeliveryQuantity']).then(async (data: Product[]) => {
          const items = await Promise.all((data as Product[]).map(async (item: Product) => {
            const imageDirectory: string = localStorage.getItem("imageDirectory");
            const resolvedImage = await Utils.resolveImageUrl(this._platform, imageDirectory, item.image);
            return { idSubCategory: item.idSubCategory, code: item.code, image: resolvedImage, name: item.name, minimumDeliveryQuantity: item.minimumDeliveryQuantity };
          }));
          const distinctItems = items.filter((value, index, self) =>
            index === self.findIndex((t) => (
                t.code === value.code
            ))
          );
          this._store.dispatch(setProducts({ items: distinctItems }));
          this._store.pipe(select('products')).subscribe(res => this.items = [...res]).unsubscribe();
        });
      }
      loading.dismiss();
    }
  }

  close(event) {
    event.stopPropagation();
    this.closeSearch.emit();
  }

  searchbarClick(event) {
    event.stopPropagation();
  }

  async startSession(selectedData: CustomerCard | ProductSearchItem) {
    if (this.simpleView && this.viewType === 'catalog') {
      if (!!selectedData)
        this.closeSearch.emit((selectedData as ProductSearchItem));
      else
        this.closeSearch.emit();
      this.searchField.setValue('');
    }
    else if (this.simpleView && this.viewType === 'customers') {
      this.closeSearch.emit((selectedData as CustomerCard).uid);
    }
    else if (!!selectedData && this.viewType === 'customers') {

      this._store.dispatch(setCurrentCustomer({ customerData: { uid: (selectedData as CustomerCard).uid, name: (selectedData as CustomerCard).name, isProspect: false } }));

      // Qui parte la navigazione al catalogo dopo aver selezionato il cliente
      this.catalogService.setIsProspect(false);
      this.catalogService.setShowFavorites(false);
      this.catalogService.setCustomerUid((selectedData as CustomerCard).uid);
      this.catalogService.setCustomerName((selectedData as CustomerCard).name);
      this.catalogService.setRootView(true);
      this.navigationService.navigateTo('/private/catalog');
    }
  }
}
