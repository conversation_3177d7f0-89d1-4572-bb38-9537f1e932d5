{"name": "dataproducts", "version": "1.0.14", "author": "P-LAB Srl", "homepage": "https://www.p-lab.it", "scripts": {"ng": "ng", "start": "ionic serve --configuration=web", "build": "ng build", "test": "ng test", "lint": "ng lint", "e2e": "ng e2e", "mobile": "ionic build && ionic cap sync"}, "private": true, "dependencies": {"@angular/animations": "^19.2.8", "@angular/common": "^19.2.8", "@angular/core": "^19.2.8", "@angular/forms": "^19.2.8", "@angular/platform-browser": "^19.2.8", "@angular/platform-browser-dynamic": "^19.2.8", "@angular/router": "^19.2.8", "@capacitor-community/sqlite": "^7.0.0", "@capacitor/android": "^7.2.0", "@capacitor/app": "^7.0.1", "@capacitor/browser": "^7.0.1", "@capacitor/core": "^7.2.0", "@capacitor/device": "^7.0.1", "@capacitor/filesystem": "^7.0.1", "@capacitor/haptics": "^7.0.1", "@capacitor/ios": "^7.2.0", "@capacitor/keyboard": "^7.0.1", "@capacitor/network": "^7.0.1", "@capacitor/screen-orientation": "^7.0.1", "@capacitor/share": "^7.0.1", "@capacitor/splash-screen": "^7.0.1", "@capacitor/status-bar": "^7.0.1", "@ionic/angular": "^8.5.5", "@ngrx/store": "^19.1.0", "@ngx-translate/core": "^14.0.0", "@ngx-translate/http-loader": "^7.0.0", "@splidejs/splide": "^4.0.6", "@tailwindcss/postcss": "^4.1.4", "@zip.js/zip.js": "^2.7.60", "capacitor-native-biometric": "^4.1.3", "jwt-decode": "^3.1.2", "postcss": "^8.5.3", "rxjs": "^7.8.2", "swiper": "^11.2.8", "tailwindcss": "^4.1.4", "tslib": "^2.2.0", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.2.9", "@angular-eslint/builder": "^19.3.0", "@angular-eslint/eslint-plugin": "^19.3.0", "@angular-eslint/eslint-plugin-template": "^19.3.0", "@angular-eslint/template-parser": "^19.3.0", "@angular/cli": "^19.2.9", "@angular/compiler": "^19.2.8", "@angular/compiler-cli": "^19.2.8", "@angular/language-service": "^19.2.8", "@capacitor/cli": "^7.2.0", "@ionic/angular-toolkit": "^4.0.0", "@ngrx/eslint-plugin": "^19.1.0", "@types/jasmine": "~3.6.0", "@types/jasminewd2": "~2.0.3", "eslint": "^9.25.1", "jasmine-core": "~3.8.0", "jasmine-spec-reporter": "~5.0.0", "karma": "~6.3.2", "karma-chrome-launcher": "~3.1.0", "karma-coverage": "~2.0.3", "karma-coverage-istanbul-reporter": "~3.0.2", "karma-jasmine": "~4.0.0", "karma-jasmine-html-reporter": "^1.5.0", "protractor": "~7.0.0", "ts-node": "~8.3.0", "typescript": "~5.8.3"}, "description": "An Ionic project"}