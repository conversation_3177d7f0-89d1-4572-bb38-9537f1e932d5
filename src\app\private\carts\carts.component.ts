import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { IonicModule, LoadingController, ToastController } from '@ionic/angular';
import { Store } from '@ngrx/store';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { CartsService } from 'src/app/service/carts/carts.service';
import { BasicResponse } from 'src/app/service/data/basic-response';
import { CartItem } from 'src/app/service/data/cart-item';
import { DbService } from 'src/app/shared/db.service';
import Utils from 'src/app/shared/utils';
import { resetCart } from 'src/app/store/actions/cart.actions';
import { CartItemPipe } from './cart-item.pipe';
import { addIcons } from 'ionicons';
import { chevronForwardOutline, cartOutline } from 'ionicons/icons';

@Component({
    selector: 'app-carts',
    templateUrl: './carts.component.html',
    styleUrls: ['./carts.component.scss'],
    standalone: true,
    imports: [
      CommonModule,
      IonicModule,
      FormsModule,
      ReactiveFormsModule,
      TranslateModule,
      CartItemPipe
    ]
})
export class CartsComponent implements OnInit {
  carts: CartItem[] = [];
  sending: boolean = false;
  constructor(private _router: Router, private _translate: TranslateService, public _loadingController: LoadingController,
    private _toastController: ToastController,
    private _dbService: DbService, private _service: CartsService,
    private _store:Store<any>) { 
      addIcons({ chevronForwardOutline, cartOutline });
    }
  
  ngOnInit() {
    this.getAllData();
  }

  async getAllData(){
    const loading = await this._loadingController.create({
      message: this._translate.instant('GENERICS.WAIT')
    });
    loading.present();
    this.carts = [] as CartItem[];
    await this._dbService.getAll(['carts CA', 'customers CU'], ['CA.*, CU.name'], ['CA.idCustomer = CU.uid']).then((data) => {
      if(!!data)
        this.carts = data.map((item: {id: number, name: string, agentCode: string, idCustomer: string, products: string})=>(
          {
            id: item.id,
            name: item.name,
            agentCode: item.agentCode,
            idCustomer: item.idCustomer,
            products: JSON.parse(item.products)
          }));
    }).finally(() => {
      loading.dismiss();
    });
  }

  cartsGoComponentGoBack(){
    this._router.navigate(['/private/home']);
  }

  goToCart(event: Event, id: number, idCustomer: string, customerName: string) {
    event.stopPropagation();
    this._router.navigate(['/private/carts/cart'], { queryParams: {
      idCart: id,
      idCustomer: idCustomer,
      customerName: customerName,
    }});
  }

  transferCart(id: number) {
    const item:CartItem = this.carts.find((item) => item.id === id);
    if(typeof item !== "undefined")
      this.doTransfer([(item)]);
  }

  transferCarts() {
    this.doTransfer(this.carts);
  }

  private doTransfer(carts:CartItem[]){
    if(!this.sending)
    {
      if(carts.filter((cart) => cart.products.filter(item => item.quantity === 0).length > 0 ).length > 0) {
        const customer = carts.filter(i => i.products.filter(a => 0 === a.quantity).length > 0)[0].name
        Utils.showSnackWithColor('toast-error-class', this._toastController, customer + ": " + this._translate.instant('CARTS.CHECK_ZEROS'));
        this.sending = false;
        return;
      }
      this.sending = true;
      this._store.dispatch(resetCart());
      const items = carts.map(({agentCode, idCustomer, products}) => {
        const entries = products.map((product) => {
          return { customer: idCustomer, product: product.idProduct, quantity: product.quantity};
        });
        return { salesman: agentCode, entries: entries };
      });
      this._service.sendOrders(items).then(async (data:BasicResponse) => {
        if(data && data.status === "OK")
        {
          if(!!data.content && !!data.content.countKo && data.content.countKo > 0 && !!data.content.errorCustomers && data.content.errorCustomers.length > 0)
          {
            const toDelete = carts.filter(saleRecord => 
              !data.content.errorCustomers.some(errorCustomer =>
                  errorCustomer.salesman === saleRecord.agentCode && errorCustomer.customer === saleRecord.idCustomer
              )
            );
            this.deleteFromDb(toDelete);
            Utils.showSnackWithColor('toast-error-class', this._toastController, this._translate.instant('CARTS.CARTS_NOT_ALL_TRANSFERED'));
          } else {
            this.deleteFromDb(carts);
            Utils.showSnack(this._toastController, this._translate.instant('CARTS.CARTS_TRANSFERED'));
          }
        } else {
          Utils.showSnackWithColor('toast-error-class', this._toastController, this._translate.instant('CARTS.CARTS_TRANSFER_ERROR'));
        }
      }).finally(()=>{
        this.sending = false;
      });
    }
  }

  private deleteFromDb(carts:CartItem[]) {
    carts.forEach(async element => {
      await this._dbService.deleteRecord("carts", [{key:'id', value: element.id}]);
      this.getAllData();
    });
  }
}
