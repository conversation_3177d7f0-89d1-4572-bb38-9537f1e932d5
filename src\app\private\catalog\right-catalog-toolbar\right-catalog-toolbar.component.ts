import { CommonModule } from '@angular/common';
import { Component, EventEmitter, inject, Input, Output } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { CardType, CatalogService, ViewType } from 'src/app/service/catalog/catalog.service';

@Component({
    selector: 'app-right-catalog-toolbar',
    templateUrl: './right-catalog-toolbar.component.html',
    styleUrls: ['./right-catalog-toolbar.component.scss'],
    standalone: true,
    imports: [
      CommonModule,
      FormsModule,
      ReactiveFormsModule,
      TranslateModule
    ]
})
export class RightCatalogToolbarComponent {

  @Input() isShowed: boolean = false;
  @Output() showNavigator = new EventEmitter();
  @Output() switchCardView = new EventEmitter();
  cardView: CardType = (!!localStorage.getItem('cardViewType') ? localStorage.getItem('cardViewType') as CardType : CardType.SMALL);
  catalogService = inject(CatalogService);

  constructor() {
    this.catalogService.cardView.subscribe((cardView) => {
      this.cardView = cardView;
      console.log(this.cardView);
    });

    // Initialize the view type in the service from localStorage
    const storedNavigationType = localStorage.getItem('catalog_navigation_type') as ViewType;
    if (storedNavigationType) {
      this.catalogService.setViewType(storedNavigationType);
    } else {
      this.catalogService.setViewType(ViewType.PAGINATED);
    }
  }

  showHide() {
    this.isShowed = !this.isShowed;
    this.showNavigator.emit(this.isShowed);
  }

  setSmallCardView() {
    console.log('setSmallCardView');
    this.cardView = CardType.SMALL;
    this.catalogService.setCardType(CardType.SMALL);
  }

  setMediumCardView() {
    console.log('setMediumCardView');
    this.cardView = CardType.MEDIUM;
    this.catalogService.setCardType(CardType.MEDIUM);
  }
  
  setLargeCardView() {
    console.log('setLargeCardView');
    this.cardView = CardType.LARGE;
    this.catalogService.setCardType(CardType.LARGE);
  }
  
}
