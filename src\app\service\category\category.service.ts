import { inject, Injectable } from '@angular/core';
import { HybridDbService } from 'src/app/shared/hybrid-db.service';
import Utils from 'src/app/shared/utils';
import { Category } from '../data/category';
import { Selection } from 'src/app/shared/selection';
import { Subcategory } from '../data/subcategory';

@Injectable({
  providedIn: 'root'
})
export class CategoryService {
  private categories: Category[] = [];
  private datacolCategories: Category[] = [];
  private _dbService = inject(HybridDbService);

  async getFilteredCategories(ids: string[]){
    await this._dbService.getAll(["categories"], ['id', 'name', 'image']).then((categories: Category[]) => {
      this.categories = categories;
    });
    this.explodeHierarchy(this.categories);
    this.datacolCategories = this.categories.filter(category => category.isProduct);

    return this.datacolCategories.filter(category => ids.includes(category.id));
  }

  async getProductCounter(idsubcategory:string, idrootcategory){
    let subcategory:Subcategory = null;
    await this._dbService.getRecordsByANDCondition('subcategories', [{key: 'idsubcategory', value: idsubcategory},{key: 'idrootcategory', value: idrootcategory}]).then((subcategories: Subcategory[]) => {
      if(!!subcategories && subcategories.length > 0)
        subcategory = subcategories[0];
    });
    return subcategory;
  }

  public async catalogByProduct(subcategories: Category[]){
    this.categories = [];
    await this.explodeHierarchy(subcategories, true);
    return this.categories;
  }

  private explodeHierarchy(categories: Category[], byProduct: boolean = false){
    if (categories && categories.length > 0) {
      categories.forEach(async (element: Category) => {
        const found = this.categories.find((item) => item.id === element.id);
        // if(!found)
        {
          if(byProduct)
          {
            if(!element.isProduct){
              if(!!element.subcategories)
              {
                const subcategories = Utils.objToJson(element.subcategories);
                if(!!subcategories && subcategories.length > 0)
                {
                  if(subcategories.filter((item:Category) =>{ return item.isProduct }).length > 0){
                    // Aggiungo un elemento di tipo categoria solo se ha dei figli prodotto, altrimenti evito
                    this.categories = this.categories.concat(element);
                  }
                  await this.explodeHierarchy(Utils.objToJson(subcategories), byProduct);
                }
              }
            }
            else if(element.isProduct) {
              this.categories = this.categories.concat(element);
            }
          }
          else
          {
            this.categories = this.categories.concat(element);
            if(!!element.subcategories)
            {
              const subcategories = Utils.objToJson(element.subcategories);
              if(!!subcategories && subcategories.length > 0)
              {
                await this.explodeHierarchy(Utils.objToJson(element.subcategories), byProduct);
              }
            }
          }
        }
      });
    }
  }

  public getProductFromNavigationHierarchy(id:string, items: Category[], navigationQueue: Selection[]){
    let lastCategoryChildren = items;
    let lastCategory: Category | null = null;
    // let lastCategoryBrothers = null;
    // la navigazione è gerarchica
    if(navigationQueue.length === 0)
    {
      // lastCategoryBrothers = [...lastCategoryChildren];
      lastCategory = items.find((item) => item.id.toString() === id);
      lastCategoryChildren = Utils.objToJson(lastCategory.subcategories);
    }
    else {
      let found = false;
      navigationQueue.forEach((element) => {
        if(!found)
        {
          // lastCategoryBrothers = [...lastCategoryChildren];
          lastCategory = lastCategoryChildren.find((item) => item.id.toString() === element.id.toString());
          lastCategoryChildren = Utils.objToJson(lastCategory.subcategories);
          if(!!lastCategory &&  !!lastCategory.id && lastCategory.id.toString() === id)
            found = true;
        }
      });
    }
    return lastCategory;
  }

  private findCategoryInSubcategories(categories:Category[], idCategory: string){
    if(!!categories)
    {
      return categories.find(item => item.id.toString() === idCategory);
    }
    return null;
  }

  private removeDuplicates(dirtyArray:Category[]) {
    var result = dirtyArray.reduce((unique, o) => {
      if(!unique.some((obj:Category) => obj.id.toString() === o.id.toString())) {
        unique.push(o);
      }
      return unique;
    },[]);
    return result;
  }

  private async findProductsInCategory(categories: Category[]) : Promise<Category[]> {
    let datacolCategories: Category[] = [];
    if (categories && categories.length > 0)  {
      await categories.reduce(async (promise, category) => {
        await promise;
          if(category.isProduct) {
            datacolCategories = [category].concat(datacolCategories);
          }
          else {
            const subcategory = !!category.subcategories ? Utils.objToJson(category.subcategories) : [];
            if(subcategory.length > 0)
            {
              const list = await this.findProductsInCategory(subcategory).then(result => result);
              datacolCategories = [...datacolCategories, ...list];
            }
          }
        }, Promise.resolve());
    }
    return new Promise((resolve) => { resolve(datacolCategories); });
  }

  // public async isFavorite(idCategory: string, customerUid: string) {
  //   var favorites = [];
  //     const finished = await this._dbService.getRecordsByANDCondition('favorites', [{key: 'customerUid', value: customerUid}]).then((data: Favorite[]) => {
  //       if(data.length > 0) {
  //         favorites = data;
  //       }
  //     });
  //   const filtered: Favorite[] = favorites.filter((favorite) => favorite.idCategory.toString() === idCategory);
  //   const found = filtered.length === 1;
  //   return found;
  // }


}
