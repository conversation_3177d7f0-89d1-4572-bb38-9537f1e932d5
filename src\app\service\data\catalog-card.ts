import { Category } from "./category";
type SimplifiedCategory = Pick<Category, 'id'|'name'|'image'|'isProduct'|'description'>
export type CatalogCardType = 'CATEGORY' | 'PRODUCT' | 'LABEL' | 'FAVORITES' | 'FAKE';
export class CatalogCard extends (Category as new () => SimplifiedCategory) {
    type: CatalogCardType = 'CATEGORY';
    isFavorite: boolean = false;
    subcategories: Category[];
    focus: string | null = null;
    divisionStatusCode: string | null = null;
    productsCount: string | null = null;
    idRootCategory: string | null = null;
    products: any[] = [];
    level?: string;

    // Nuovi campi dalla sincronizzazione v2
    idCatalog?: number = null;
    idApp?: string = null;
    idParent?: string = null;
    idSubCategory?: string = null;
    idImage?: number = null;
    imageUrl?: string = null;
    isRootCategory?: boolean = null;
    isRootTree?: boolean = null;
    modifiedInstant?: number = null;
    isRootCategoryByRegex?: boolean = null;

    constructor(
        id: string,
        idRootCategory: string | null,
        name: string,
        image: string,
        isProduct: boolean,
        type: CatalogCardType = 'CATEGORY',
        subcategories: Category[] = [],
        isFavorite: boolean = false,
        description: string,
        focus: string | null = null,
        productsCount: string | null = null,
        divisionStatusCode: string | null = null,
        level?: string,
        // Nuovi parametri opzionali per i campi v2
        idCatalog?: number,
        idApp?: string,
        idParent?: string,
        idSubCategory?: string,
        idImage?: number,
        imageUrl?: string,
        isRootCategory?: boolean,
        isRootTree?: boolean,
        modifiedInstant?: number,
        isRootCategoryByRegex?: boolean
    ) {
        super();
        this.id = id;
        this.idRootCategory = idRootCategory;
        this.name = name;
        this.image = image;
        this.isProduct = isProduct;
        this.type = type;
        this.subcategories = subcategories;
        this.isFavorite = isFavorite;
        this.description = description;
        this.focus = focus;
        this.productsCount = productsCount;
        this.divisionStatusCode = divisionStatusCode;
        this.level = level;

        // Assegna i nuovi campi v2
        this.idCatalog = idCatalog;
        this.idApp = idApp;
        this.idParent = idParent;
        this.idSubCategory = idSubCategory;
        this.idImage = idImage;
        this.imageUrl = imageUrl;
        this.isRootCategory = isRootCategory;
        this.isRootTree = isRootTree;
        this.modifiedInstant = modifiedInstant;
        this.isRootCategoryByRegex = isRootCategoryByRegex;
    }

    /**
     * Crea una CatalogCard da un oggetto Category
     * @param category Oggetto Category da convertire
     * @param type Tipo di card (default: 'CATEGORY')
     * @param isFavorite Se la card è nei preferiti (default: false)
     * @returns Nuova istanza di CatalogCard
     */
    static fromCategory(category: Category, type: CatalogCardType = 'CATEGORY', isFavorite: boolean = false): CatalogCard {
        // Gestisce la conversione delle subcategories che possono essere una stringa JSON
        let subcategoriesArray: Category[] = [];
        if (category.subcategories) {
            if (typeof category.subcategories === 'string') {
                try {
                    subcategoriesArray = JSON.parse(category.subcategories);
                } catch (error) {
                    console.warn('Errore nel parsing delle subcategories:', error);
                    subcategoriesArray = [];
                }
            } else if (Array.isArray(category.subcategories)) {
                subcategoriesArray = category.subcategories;
            }
        }

        return new CatalogCard(
            category.id,
            category.idRootCategory?.toString() || null,
            category.name,
            category.image,
            category.isProduct,
            type,
            subcategoriesArray,
            isFavorite,
            category.description,
            category.focus?.toString() || null,
            category.productsCount?.toString() || null,
            category.divisionStatusCode,
            category.level,
            // Nuovi campi v2
            category.idCatalog,
            category.idApp,
            category.idParent,
            category.idSubCategory,
            category.idImage,
            category.imageUrl,
            category.isRootCategory,
            category.isRootTree,
            category.modifiedInstant,
            category.isRootCategoryByRegex
        );
    }
}
