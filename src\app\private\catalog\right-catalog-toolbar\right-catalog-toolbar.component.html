<div class="right-bar">
  <div class="top-cell">
    <button (click)="showHide()" class="visible">
      <object type="image/svg+xml" data="../../assets/svg/burger.svg">
        <img src="../../assets/svg/burger.svg" />
      </object>
    </button>
  </div>
  @if(catalogService.isPaginatedView && !catalogService.isRootView) {
  <div class="bottom-cell">
    <button (click)="setSmallCardView()" [ngClass]="{ 'visible' : !catalogService.isSmallCardView}">
      <object type="image/svg+xml" data="../../assets/svg/catalog-small.svg">
        <img src="../../assets/svg/catalog-small.svg" />
      </object>
    </button>
    <button (click)="setMediumCardView()" [ngClass]="{ 'visible' : !catalogService.isMediumCardView}">
      <object type="image/svg+xml" data="../../assets/svg/catalog-medium.svg">
        <img src="../../assets/svg/catalog-medium.svg" />
      </object>
    </button>
    <button (click)="setLargeCardView()" [ngClass]="{ 'visible' : !catalogService.isLargeCardView}">
      <object type="image/svg+xml" data="../../assets/svg/catalog-large.svg">
        <img src="../../assets/svg/catalog-large.svg" />
      </object>
    </button>
  </div>
  } 
</div>
