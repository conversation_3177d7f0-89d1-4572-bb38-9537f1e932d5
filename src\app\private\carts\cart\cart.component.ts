import { Component, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { AlertController, IonicModule, LoadingController, ToastController } from '@ionic/angular';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { CartsService } from 'src/app/service/carts/carts.service';
import { BasicResponse } from 'src/app/service/data/basic-response';
import { CartItem } from 'src/app/service/data/cart-item';
import { Product } from 'src/app/service/data/product';
import { ProductInCart } from 'src/app/service/data/product-in-cart';
import { DbService } from 'src/app/shared/db.service';
import { Platform } from '@ionic/angular';
import Utils from 'src/app/shared/utils';
import { setCurrentCustomer } from 'src/app/store/actions/current-customer.actions';
import { select, Store } from '@ngrx/store';
import { LocalStorageService } from 'src/app/service/local-storage.service';
import { resetCart, setCart } from 'src/app/store/actions/cart.actions';
import { Cart } from 'src/app/service/data/cart';
import { Category } from 'src/app/service/data/category';
import { Keyboard, KeyboardResize } from '@capacitor/keyboard';
import { CommonModule } from '@angular/common';
import { SearchComponent } from '../../search/search.component';
import { SafePipe } from 'src/app/shared/safe-pipe';
import { ZeroRemoverPipe } from 'src/app/shared/zero-remover.pipe';
import { addIcons } from 'ionicons';
import { calculatorOutline, removeOutline, addOutline, trashOutline } from 'ionicons/icons';

@Component({
    selector: 'app-cart',
    templateUrl: './cart.component.html',
    styleUrls: ['./cart.component.scss'],
    standalone: true,
    imports: [
      IonicModule,
      TranslateModule,
      CommonModule,
      SearchComponent,
      SafePipe,
      ZeroRemoverPipe
    ]
})
export class CartComponent implements OnInit, OnDestroy {
  cart: CartItem = null;
  cartId: string = null;
  idCustomer: string = null;
  customerName: string = null;
  imageDirectory: string = localStorage.getItem('imageDirectory');
  sending: boolean = false;

  constructor(private _platform: Platform, private _router: Router, private _activatedRoute: ActivatedRoute, private _translate: TranslateService,
    public _loadingController: LoadingController, private _alertController: AlertController,
    private _toastController: ToastController, private _lsService: LocalStorageService,
    private _dbService: DbService, private _service: CartsService, private _store: Store<any>) { 
      addIcons({ calculatorOutline, removeOutline, addOutline, trashOutline });
    }

  async ngOnInit() {
    this._activatedRoute.queryParams
      .subscribe((params : {idCart: string, idCustomer: string, customerName: string}) => {
        this.cartId = params.idCart;
        this._store.dispatch(setCurrentCustomer({customerData: { uid: params.idCustomer, name: params.customerName, isProspect: false }}));
        this.idCustomer = params.idCustomer;
        this.customerName = params.customerName;
      }
    );
    this.getAllData();
    if (this._platform.is('capacitor')) {
      Keyboard.removeAllListeners();
      Keyboard.addListener('keyboardWillShow', (info) => {
        document.querySelector('.container').setAttribute('style', `transform: translateY(${-document.activeElement.getBoundingClientRect().top + 200}px)`); // 65 è per la barra di stato
      });
      
      Keyboard.addListener('keyboardWillHide', () => { 
        document.querySelector('.container').setAttribute('style', `transform: translateY(0px)`);
      });
    }
  }

  ngOnDestroy(): void {
    Keyboard.removeAllListeners();
  }


  cartGoComponentGoBack(){

    let routingStack = [];
    this._store.pipe(select('routingStack')).subscribe(res => routingStack = [...res]).unsubscribe();
    const lastRouting = routingStack[routingStack.length - 2];
    let navHistory = [];
    this._store.pipe(select('navHistory')).subscribe(res => navHistory = res).unsubscribe();
    const lastHistory = navHistory[navHistory.length - 1];
    let currentCustomer = null;
    this._store.pipe(select('currentCustomer')).subscribe(res => currentCustomer = res).unsubscribe();

    if(lastRouting.component.includes('catalog') && !lastRouting.component.includes('product'))
    {
      console.log('cartGoComponentGoBack 1', lastRouting);
      this._router.navigate(['/private/catalog'], { queryParams: {
        customerUid: currentCustomer.uid,
        customerName: currentCustomer.name,
        isProspect: lastRouting.component.includes('isProspect=true'),
        showFavorites: false,
        clickedNavarId: !!lastHistory ? lastHistory.item.id : null,
        turnAround: true
      }});
    } else if (lastRouting.component.includes('product')) {
      console.log('cartGoComponentGoBack 2', lastRouting);
      this._router.navigate([`/private/catalog/products-carousel/${lastHistory.item.id}`], { queryParams: {
        idRootCategory: lastHistory.item.datacolCategory.category.idRootCategory,
        current: JSON.stringify(lastHistory.item.datacolCategory.category),
        id: lastHistory.item.id,
        customerUid: currentCustomer.uid,
        customerName: currentCustomer.name,
        isFavorite: lastHistory.item.datacolCategory.isFavorite,
        isProspect: lastRouting.component.includes('isProspect=true'),
      }})
    }
    else
      this._router.navigate(['/private/carts']);
  }

  async getAllData(){
    const loading = await this._loadingController.create({
      message: this._translate.instant('GENERICS.WAIT'),
    });
    loading.present();
    this.cart = null;
    await this._dbService.getRecordsByANDCondition('carts', [{key: 'id', value: this.cartId}]).then((data) => {
      if(!!data && data.length === 1)
      {
        this.cart = data.map((item: {id: number, name: string, agentCode: string, idCustomer: string, products: string})=>(
          {
            id: item.id,
            name: item.name,
            agentCode: item.agentCode,
            idCustomer: item.idCustomer,
            products: JSON.parse(item.products).map((product) => product)
          }))[0];
          this.cart.products.forEach(async (product: ProductInCart, index) => {
            this._dbService.getRecordsByANDCondition('products', [{key: 'code', value: product.idProduct}]).then(async (products:Product[]) => {
              if(!!products && products.length >= 1)
              {
                (this.cart.products[index]).description = products[0].name;
                (this.cart.products[index]).minimumDeliveryQuantity = +products[0].minimumDeliveryQuantity;
                (this.cart.products[index]).image = await Utils.resolveImageUrl(this._platform, this.imageDirectory, products[0].image);
              }
            });
          });
      }
    }).finally(() => {
      loading.dismiss()
    });
  }

  transferCarts() {
    if(!this.sending)
    {
      this.sending = true;
      this.doTransfer([this.cart]);
    }
  }

  private doTransfer(carts:CartItem[]){
    if(carts.filter((cart) => cart.products.filter(item => item.quantity === 0).length > 0 ).length > 0){
      Utils.showSnackWithColor('toast-error-class', this._toastController, this._translate.instant('CARTS.CHECK_ZEROS'));
      this.sending = false;
      return;
    }
    const items = carts.map(({agentCode, idCustomer, products}) => {
      const entries = products.map((product) => {
        return { customer: idCustomer, product: product.idProduct, quantity: product.quantity};
      });
      return { salesman: agentCode, entries: entries };
    });
    this._service.sendOrders(items).then(async (data:BasicResponse) => {
      if(data && data.status === "OK")
      {
        if(!!data.content && !!data.content.countKo && data.content.countKo > 0 && !!data.content.errorCustomers && data.content.errorCustomers.length > 0)
        {
          const toDelete = carts.filter(saleRecord => 
            !data.content.errorCustomers.some(errorCustomer =>
                errorCustomer.salesman === saleRecord.agentCode && errorCustomer.customer === saleRecord.idCustomer
            )
          );
          this.deleteFromDb(toDelete);
          Utils.showSnackWithColor('toast-error-class', this._toastController, this._translate.instant('CARTS.CARTS_NOT_ALL_TRANSFERED'));
        } else {
          this.deleteFromDb(carts);
          this._store.dispatch(resetCart());
          Utils.showSnack(this._toastController, this._translate.instant('CARTS.CARTS_TRANSFERED'));
          this._router.navigate(['/private/carts']);
        }
      } else {
        Utils.showSnack(this._toastController, this._translate.instant('CARTS.CARTS_TRANSFER_ERROR'));
      }
      this.sending = false;
    }).catch(()=> {
      this.sending = false;
    });
  }

  private deleteFromDb(carts:CartItem[]) {
    carts.forEach(async element => {
      await this._dbService.deleteRecord("carts", [{key:'id', value: element.id}]);
      this.getAllData();
    });
  }

  async addQuantity(idProduct:string) {
    const tmpProducts = this.cart.products.map((item) => {
      if(item.idProduct.toString() === idProduct.toString())
      {
        item.quantity = item.quantity + item.minimumDeliveryQuantity;
        return item;
      } else
        return item;
    })
    await this._dbService.updateRecord('carts', [{key: 'id', value: this.cartId}], [{key: 'products', value: JSON.stringify(tmpProducts)}]).then(_=>{
      this.cart.products = tmpProducts;
      this._store.dispatch(setCart( { cart: {idCustomer: this.idCustomer, idCart: +this.cartId, currentQuantityInCart: (tmpProducts.length) } }));
    });
  }

  async removeQuantity(idProduct:string, toDelete:boolean) {
    if(toDelete)
      this.removeProduct(idProduct);
    else {
      const tmpProducts = this.cart.products.map((item) => {
        if(item.idProduct === idProduct)
        {
          item.quantity = item.quantity - item.minimumDeliveryQuantity;
          return item;
        } else
          return item;
      })
      await this._dbService.updateRecord('carts', [{key: 'id', value: this.cartId}], [{key: 'products', value: JSON.stringify(tmpProducts)}]).then(_=>{
        this.cart.products = tmpProducts;
        this._store.dispatch(setCart( { cart: {idCustomer: this.idCustomer, idCart: +this.cartId, currentQuantityInCart: (tmpProducts.length) } }));
      });
    }
  }

  async removeProduct(idProduct:string){
    const alert = await this._alertController.create({
      header: this._translate.instant('CARTS.CONF_TITLE'),
      message: this._translate.instant('CARTS.CONF_MSG'),
      buttons: [
        {
          text: this._translate.instant('CARTS.CANCEL'),
          role: 'cancel',
          cssClass: 'secondary',
          id: 'cancel-button',
          handler: (blah) => {
          }
        }, {
          text: this._translate.instant('CARTS.CONFIRM'),
          id: 'confirm-button',
          handler: async () => {
            const tmpProducts = this.cart.products.filter((item) => item.idProduct !== idProduct);
            if(tmpProducts.length > 0)
            {
              await this._dbService.updateRecord('carts', [{key: 'id', value: this.cartId}], [{key: 'products', value: JSON.stringify(tmpProducts)}]).then(async _=>{
                this.cart.products = tmpProducts;
                await this._dbService.getRecordsByANDCondition('carts', [{key: 'idCustomer', value: this.idCustomer}]).then(async (data:Cart[]) => {
                  if(!!data[0] && !!data[0]['id']) {
                    const rows = data[0];
                    this._store.dispatch(setCart( { cart: {idCustomer: this.idCustomer, idCart: rows.id, currentQuantityInCart: (tmpProducts.length) } }));
                  }
                });
              });
            } else {
              this.deleteCart();
            }
          }
        }
      ]
    });
    await alert.present();
  }

  private async deleteCart(){
    await this._dbService.deleteRecord('carts', [{key: 'id', value: this.cartId}]).then(_=>{
      Utils.showSnack(this._toastController, this._translate.instant('CARTS.CART_EMPTY'));
      this._store.dispatch(resetCart());
      this._router.navigate(['/private/carts']);
    });
  }

  multiplier(idProduct:string, input:any) {
    if (+input === 0) {
      Utils.showSnackWithColor('toast-error-class', this._toastController, this._translate.instant("CARTS.CHECK_NO_ZEROS"), 'OK', true);
      return;
    }
    // con il valore dato in input, assegnna item.quantity = item.quantity + 10
    const tmpProducts = this.cart.products.map((item) => {
      if(item.idProduct === idProduct)
      {
        // item.quantity = +event.target.value * +item.minimumDeliveryQuantity;
        let multi = 0;
         // Controlla se il valore è già un multiplo di quantitaMinimaOrdinabile
          if (+input % +item.minimumDeliveryQuantity === 0) {
            multi = +input;
          } else {
            // Calcola il multiplo superiore
            multi = Math.ceil(+input / +item.minimumDeliveryQuantity) * +item.minimumDeliveryQuantity;
          }
        item.quantity = multi;
        return item;
      } else
        return item;
    });
    this._dbService.updateRecord('carts', [{key: 'id', value: this.cartId}], [{key: 'products', value: JSON.stringify(tmpProducts)}]).then(_=>{
      this.cart.products = tmpProducts;
      this._store.dispatch(setCart( { cart: {idCustomer: this.idCustomer, idCart: +this.cartId, currentQuantityInCart: (tmpProducts.length) } }));

      
    });

  }

  async closeProductSearch(selectedProduct:Product) {
    if(!!selectedProduct)
    {
      if(this.cart.products.filter((item) => item.idProduct === selectedProduct.code).length == 0)
      {
        let newProduct:ProductInCart  = {
          idProduct: selectedProduct.code,
          quantity: +selectedProduct.minimumDeliveryQuantity,
          description: selectedProduct.name,
          image: selectedProduct.image,
          minimumDeliveryQuantity: +selectedProduct.minimumDeliveryQuantity
        }
        this.cart.products.push(newProduct);
        this._dbService.updateRecord('carts', [{key: 'id', value: this.cartId}], [{key: 'products', value: JSON.stringify(this.cart.products)}]).then(_=>{
          this._store.dispatch(setCart( { cart: {idCustomer: this.idCustomer, idCart: +this.cartId, currentQuantityInCart: (this.cart.products.length) } }));
        });
      } else {
        Utils.showSnackWithColor('toast-error-class', this._toastController, this._translate.instant("PRODUCT.ITEM_YET_IN_CART") + selectedProduct.code.replace(/^0+/, ''));
      }
    }
  }
}
