import { Injectable } from '@angular/core';
import { Platform } from '@ionic/angular';
import { DbService } from './db.service';
import { IndexedDbService } from './indexeddb.service';
import { TableProperty } from './table-property';

@Injectable({
  providedIn: 'root'
})
export class HybridDbService {
  private indexedDbAvailable: boolean | null = null;

  constructor(
    private platform: Platform,
    private sqliteDbService: DbService,
    private indexedDbService: IndexedDbService
  ) {}

  /**
   * Ricrea il database in caso di problemi di versione
   */
  async recreateDatabase(): Promise<void> {
    if (this.isWeb()) {
      return this.indexedDbService.recreateDatabase();
    } else {
      return Promise.resolve();
    }
  }

  private isWeb(): boolean {
    return !this.platform.is('capacitor');
  }

  /**
   * Verifica se IndexedDB è disponibile (solo per web)
   */
  private async checkIndexedDbAvailability(): Promise<boolean> {
    if (this.indexedDbAvailable !== null) {
      return this.indexedDbAvailable;
    }

    if (!this.isWeb()) {
      this.indexedDbAvailable = false;
      return false;
    }

    try {
      this.indexedDbAvailable = await this.indexedDbService.isAvailable();
      return this.indexedDbAvailable;
    } catch (error) {
      console.error('Error checking IndexedDB availability:', error);
      this.indexedDbAvailable = false;
      return false;
    }
  }

  async createTable(tableName: string, columnList: TableProperty[], withAutomaticId: boolean = false): Promise<any> {
    if (this.isWeb()) {
      return this.indexedDbService.createTable(tableName, columnList, withAutomaticId);
    } else {
      return this.sqliteDbService.createTable(tableName, columnList, withAutomaticId);
    }
  }

  async createUniqueIndex(tableName: string, columnList: string[]): Promise<any> {
    if (this.isWeb()) {
      return this.indexedDbService.createUniqueIndex(tableName, columnList);
    } else {
      return this.sqliteDbService.createUniqueIndex(tableName, columnList);
    }
  }

  async createIndex(tableName: string, columnList: string[]): Promise<any> {
    if (this.isWeb()) {
      return this.indexedDbService.createIndex(tableName, columnList);
    } else {
      return this.sqliteDbService.createIndex(tableName, columnList);
    }
  }

  async addColumn(tableName: string, columnName: string, columnType: string): Promise<any> {
    if (this.isWeb()) {
      return this.indexedDbService.addColumn(tableName, columnName, columnType);
    } else {
      return this.sqliteDbService.addColumn(tableName, columnName, columnType);
    }
  }

  async clearTable(tableName: string): Promise<any> {
    if (this.isWeb()) {
      const isAvailable = await this.checkIndexedDbAvailability();
      if (!isAvailable) {
        console.warn(`⚠️ IndexedDB not available, skipping clearTable for ${tableName}`);
        return Promise.resolve(null);
      }

      try {
        return await this.indexedDbService.clearTable(tableName);
      } catch (error) {
        console.error(`❌ Failed to clear table ${tableName} in IndexedDB:`, error);
        // Non lanciare l'errore, ma logga e continua
        return Promise.resolve(null);
      }
    } else {
      return this.sqliteDbService.clearTable(tableName);
    }
  }

  async addRecord(tableName: string, columnList: string[], valueList: any[]): Promise<any> {
    if (this.isWeb()) {
      const isAvailable = await this.checkIndexedDbAvailability();
      if (!isAvailable) {
        console.warn(`⚠️ IndexedDB not available, skipping addRecord for ${tableName}`);
        return Promise.resolve(null);
      }

      try {
        return await this.indexedDbService.addRecord(tableName, columnList, valueList);
      } catch (error) {
        console.error(`❌ Failed to add record to ${tableName} in IndexedDB:`, error);
        return Promise.resolve(null);
      }
    } else {
      return this.sqliteDbService.addRecord(tableName, columnList, valueList);
    }
  }

  async addRecords(tableName: string, columns: string[], records: any[][]): Promise<any> {
    if (this.isWeb()) {
      const isAvailable = await this.checkIndexedDbAvailability();
      if (!isAvailable) {
        console.warn(`⚠️ IndexedDB not available, skipping addRecords for ${tableName} (${records.length} records)`);
        return Promise.resolve(null);
      }

      try {
        return await this.indexedDbService.addRecords(tableName, columns, records);
      } catch (error) {
        console.error(`❌ Failed to add records to ${tableName} in IndexedDB:`, error);
        return Promise.resolve(null);
      }
    } else {
      return this.sqliteDbService.addRecords(tableName, columns, records);
    }
  }

  async replaceIntoRecord(tableName: string, columnList: string[], valueList: any[]): Promise<any> {
    if (this.isWeb()) {
      return this.indexedDbService.replaceIntoRecord(tableName, columnList, valueList);
    } else {
      return this.sqliteDbService.replaceIntoRecord(tableName, columnList, valueList);
    }
  }

  async multipleInsertOrReplaceByTransaction(
    tableName: string,
    isIdAutoincrement: boolean,
    columns: string[],
    records: { andConditionList: { key: string, value: any }[]; values: any[] }[]
  ): Promise<any> {
    if (this.isWeb()) {
      return this.indexedDbService.multipleInsertOrReplaceByTransaction(tableName, isIdAutoincrement, columns, records);
    } else {
      return this.sqliteDbService.multipleInsertOrReplaceByTransaction(tableName, isIdAutoincrement, columns, records);
    }
  }

  async insertOrReplace(tableName: string, andConditionList: {key: string, value: any}[], columnList: string[], valueList: any[]): Promise<any> {
    if (this.isWeb()) {
      // Per IndexedDB, usiamo replaceIntoRecord che fa la stessa cosa
      return this.indexedDbService.replaceIntoRecord(tableName, columnList, valueList);
    } else {
      return this.sqliteDbService.insertOrReplace(tableName, andConditionList, columnList, valueList);
    }
  }

  async insertOrReplaceWithoutID(tableName: string, andConditionList: {key: string, value: any}[], columnList: string[], valueList: any[]): Promise<any> {
    if (this.isWeb()) {
      return this.indexedDbService.replaceIntoRecord(tableName, columnList, valueList);
    } else {
      return this.sqliteDbService.insertOrReplaceWithoutID(tableName, andConditionList, columnList, valueList);
    }
  }

  async getDistinct(tableName: string, columnList?: string[]): Promise<any[]> {
    if (this.isWeb()) {
      // Per IndexedDB, otteniamo tutti i record e filtriamo i duplicati
      const allRecords = await this.indexedDbService.getAll([tableName], columnList);
      
      if (!columnList || columnList.length === 0) {
        return allRecords;
      }
      
      // Rimuovi duplicati basandoti sulle colonne specificate
      const seen = new Set();
      return allRecords.filter(record => {
        const key = columnList.map(col => record[col]).join('|');
        if (seen.has(key)) {
          return false;
        }
        seen.add(key);
        return true;
      });
    } else {
      return this.sqliteDbService.getDistinct(tableName, columnList);
    }
  }

  async getAll(tableName: string[], columnList?: string[], joinList?: string[]): Promise<any[]> {
    if (this.isWeb()) {
      const isAvailable = await this.checkIndexedDbAvailability();
      if (!isAvailable) {
        console.warn(`⚠️ IndexedDB not available, returning empty array for ${tableName[0]}`);
        return [];
      }

      try {
        return await this.indexedDbService.getAll(tableName, columnList, joinList);
      } catch (error) {
        console.error(`❌ Failed to get records from ${tableName[0]} in IndexedDB:`, error);
        return [];
      }
    } else {
      return this.sqliteDbService.getAll(tableName, columnList, joinList);
    }
  }

  async getAllGroupedBy(tableName: string, columnList?: string[], groupBy?: string[]): Promise<any[]> {
    if (this.isWeb()) {
      // Per IndexedDB, otteniamo tutti i record e li raggruppiamo manualmente
      const allRecords = await this.indexedDbService.getAll([tableName], columnList);
      
      if (!groupBy || groupBy.length === 0) {
        return allRecords;
      }
      
      // Raggruppa manualmente
      const groups = new Map();
      allRecords.forEach(record => {
        const key = groupBy.map(col => record[col]).join('|');
        if (!groups.has(key)) {
          groups.set(key, record);
        }
      });
      
      return Array.from(groups.values());
    } else {
      return this.sqliteDbService.getAllGroupedBy(tableName, columnList, groupBy);
    }
  }

  async getRecordsByANDCondition(tableName: string, conditionalValueList: {key: string, value: string}[]): Promise<any[]> {
    if (this.isWeb()) {
      return this.indexedDbService.getRecordsByANDCondition(tableName, conditionalValueList);
    } else {
      return this.sqliteDbService.getRecordsByANDCondition(tableName, conditionalValueList);
    }
  }

  async getColumnFromTableByANDCondition(tableName: string, columnList: string[], conditionalValueList: {key: string, value: string}[]): Promise<any[]> {
    if (this.isWeb()) {
      const records = await this.indexedDbService.getRecordsByANDCondition(tableName, conditionalValueList);
      
      // Filtra solo le colonne richieste
      return records.map(record => {
        const filtered: any = {};
        columnList.forEach(col => {
          if (record.hasOwnProperty(col)) {
            filtered[col] = record[col];
          }
        });
        return filtered;
      });
    } else {
      return this.sqliteDbService.getColumnFromTableByANDCondition(tableName, columnList, conditionalValueList);
    }
  }

  async getColumnFromTableByORCondition(tableName: string, columnList: string[], conditionalValueList: {key: string, value: string}[]): Promise<any[]> {
    if (this.isWeb()) {
      const allRecords = await this.indexedDbService.getAll([tableName]);
      
      // Filtra con condizioni OR
      const filtered = allRecords.filter(record => {
        return conditionalValueList.some(condition => {
          return record[condition.key] && record[condition.key].toString() === condition.value;
        });
      });
      
      // Filtra solo le colonne richieste
      return filtered.map(record => {
        const result: any = {};
        columnList.forEach(col => {
          if (record.hasOwnProperty(col)) {
            result[col] = record[col];
          }
        });
        return result;
      });
    } else {
      return this.sqliteDbService.getColumnFromTableByORCondition(tableName, columnList, conditionalValueList);
    }
  }

  async getRecordsByINCondition(tableName: string, conditionalValueList: {key: string, value: string[]}): Promise<any[]> {
    if (this.isWeb()) {
      const allRecords = await this.indexedDbService.getAll([tableName]);
      
      // Filtra con condizione IN
      return allRecords.filter(record => {
        return conditionalValueList.value.includes(record[conditionalValueList.key]);
      });
    } else {
      return this.sqliteDbService.getRecordsByINCondition(tableName, conditionalValueList);
    }
  }

  async deleteRecord(tableName: string, andConditionList: {key: string, value: any}[]): Promise<any> {
    if (this.isWeb()) {
      return this.indexedDbService.deleteRecord(tableName, andConditionList);
    } else {
      return this.sqliteDbService.deleteRecord(tableName, andConditionList);
    }
  }

  async updateRecord(tableName: string, andConditionList: {key: string, value: any}[], columnList: {key: string, value: any}[]): Promise<any> {
    if (this.isWeb()) {
      // Per IndexedDB, otteniamo il record, lo modifichiamo e lo salviamo
      const records = await this.indexedDbService.getRecordsByANDCondition(
        tableName, 
        andConditionList.map(cond => ({ key: cond.key, value: cond.value.toString() }))
      );
      
      if (records.length > 0) {
        const record = records[0];
        columnList.forEach(col => {
          record[col.key] = col.value;
        });
        
        const columns = Object.keys(record);
        const values = Object.values(record);
        return this.indexedDbService.replaceIntoRecord(tableName, columns, values);
      }
      
      return Promise.resolve(null);
    } else {
      return this.sqliteDbService.updateRecord(tableName, andConditionList, columnList);
    }
  }
}
