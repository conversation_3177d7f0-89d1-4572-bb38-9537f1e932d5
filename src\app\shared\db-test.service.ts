import { Injectable } from '@angular/core';
import { Platform } from '@ionic/angular';
import { Store } from '@ngrx/store';
import { IndexedDbService } from './indexeddb.service';
import { HybridDbService } from './hybrid-db.service';
import { SyncroV2DbService } from '../service/syncro-v2/syncro-v2-db.service';

@Injectable({
  providedIn: 'root'
})
export class DbTestService {

  constructor(
    private platform: Platform,
    private indexedDbService: IndexedDbService,
    private hybridDbService: HybridDbService,
    private syncroV2DbService: SyncroV2DbService,
    private store: Store<any>
  ) {}

  /**
   * Testa la disponibilità e funzionalità del database
   */
  async testDatabaseAvailability(): Promise<{
    platform: string;
    indexedDbSupported: boolean;
    indexedDbAvailable: boolean;
    hybridDbWorking: boolean;
    errors: string[];
  }> {
    const result = {
      platform: this.platform.is('capacitor') ? 'mobile' : 'web',
      indexedDbSupported: false,
      indexedDbAvailable: false,
      hybridDbWorking: false,
      errors: [] as string[]
    };

    try {
      // Test 1: Verifica supporto IndexedDB
      if (typeof window !== 'undefined' && window.indexedDB) {
        result.indexedDbSupported = true;
      } else {
        result.errors.push('IndexedDB non è supportato in questo browser');
      }

      // Test 2: Verifica disponibilità IndexedDB (solo su web)
      if (result.platform === 'web' && result.indexedDbSupported) {
        try {
          result.indexedDbAvailable = await this.indexedDbService.isAvailable();
          if (result.indexedDbAvailable) {
          } else {
            result.errors.push('IndexedDB non è disponibile (potrebbe essere modalità privata o storage pieno)');
          }
        } catch (error) {
          result.errors.push(`Errore nel test IndexedDB: ${error.message}`);
          console.error('❌ Errore nel test IndexedDB:', error);
        }
      }

      // Test 3: Verifica funzionalità HybridDbService
      try {
        // Test di lettura (dovrebbe funzionare anche se le tabelle non esistono)
        const testData = await this.hybridDbService.getAll(['categories'], ['id']);
        result.hybridDbWorking = true;
      } catch (error) {
        result.errors.push(`Errore nel test HybridDbService: ${error.message}`);
        console.error('❌ Errore nel test HybridDbService:', error);
      }

      // Test 4: Verifica esistenza tabelle principali
      if (result.hybridDbWorking) {
        try {
          await this.testTableExistence();
        } catch (error) {
          result.errors.push(`Errore nel test tabelle: ${error.message}`);
          console.warn('⚠️ Test tabelle fallito:', error);
        }
      }

      // Test 5: Test di scrittura (solo se tutto funziona)
      if (result.hybridDbWorking) {
        try {
          await this.testWriteOperation();
        } catch (error) {
          result.errors.push(`Errore nel test di scrittura: ${error.message}`);
          console.warn('⚠️ Test di scrittura fallito:', error);
        }
      }

    } catch (error) {
      result.errors.push(`Errore generale nel test database: ${error.message}`);
      console.error('❌ Errore generale nel test database:', error);
    }

    return result;
  }

  /**
   * Test per verificare l'esistenza delle tabelle principali
   */
  private async testTableExistence(): Promise<void> {
    const requiredTables = ['customers', 'categories', 'products', 'userSettings'];

    for (const tableName of requiredTables) {
      try {
        const records = await this.hybridDbService.getAll([tableName], ['id']);
      } catch (error) {
        console.warn(`⚠️ Problema con tabella ${tableName}:`, error);
        throw new Error(`Tabella ${tableName} non accessibile: ${error.message}`);
      }
    }
  }

  /**
   * Test di scrittura per verificare che il database sia scrivibile
   */
  private async testWriteOperation(): Promise<void> {
    const testTableName = 'userSettings'; // Usa una tabella esistente
    const testData = {
      id: 'test_' + Date.now(),
      username: 'test_user',
      type: 'test_type',
      value: 'test_value',
      instant: Date.now().toString()
    };

    try {
      // Prova a scrivere un record di test
      await this.hybridDbService.addRecord(
        testTableName,
        ['id', 'username', 'type', 'value', 'instant'],
        [testData.id, testData.username, testData.type, testData.value, testData.instant]
      );

      // Prova a leggere il record
      const records = await this.hybridDbService.getAll([testTableName]);
      const foundRecord = records.find(r => r.id === testData.id);

      if (foundRecord) {

        // Pulisci il record di test
        try {
          await this.hybridDbService.deleteRecord(testTableName, [{ key: 'id', value: testData.id }]);
        } catch (cleanupError) {
          console.warn('⚠️ Errore nella pulizia del record di test:', cleanupError);
        }
      } else {
        throw new Error('Record di test non trovato dopo la scrittura');
      }

    } catch (error) {
      throw new Error(`Test di scrittura fallito: ${error.message}`);
    }
  }

  /**
   * Mostra un report dettagliato dello stato del database
   */
  async showDatabaseReport(): Promise<string> {
    const testResult = await this.testDatabaseAvailability();
    
    let report = `📊 REPORT DATABASE\n`;
    report += `Platform: ${testResult.platform}\n`;
    report += `IndexedDB Supportato: ${testResult.indexedDbSupported ? '✅' : '❌'}\n`;
    report += `IndexedDB Disponibile: ${testResult.indexedDbAvailable ? '✅' : '❌'}\n`;
    report += `HybridDB Funzionante: ${testResult.hybridDbWorking ? '✅' : '❌'}\n`;
    
    if (testResult.errors.length > 0) {
      report += `\n⚠️ ERRORI:\n`;
      testResult.errors.forEach(error => {
        report += `- ${error}\n`;
      });
    }

    if (testResult.hybridDbWorking) {
      report += `\n✅ Il database è pronto per la sincronizzazione`;
    } else {
      report += `\n❌ Il database ha problemi, la sincronizzazione potrebbe non funzionare correttamente`;
    }

    console.log(report);
    return report;
  }

  /**
   * Test completo del sistema unificato database + syncrov2
   */
  async testUnifiedDatabaseSystem(): Promise<{
    platform: string;
    databaseWorking: boolean;
    syncroV2Compatible: boolean;
    storeIntegration: boolean;
    errors: string[];
    warnings: string[];
  }> {
    const result = {
      platform: this.platform.is('capacitor') ? 'mobile' : 'web',
      databaseWorking: false,
      syncroV2Compatible: false,
      storeIntegration: false,
      errors: [] as string[],
      warnings: [] as string[]
    };

    console.log('🧪 Avvio test completo del sistema unificato...');

    try {
      // Test 1: Verifica funzionalità database di base
      console.log('📋 Test 1: Verifica database di base...');
      const dbTest = await this.testDatabaseAvailability();
      result.databaseWorking = dbTest.hybridDbWorking;

      if (!result.databaseWorking) {
        result.errors.push('Database di base non funzionante');
        return result;
      }

      // Test 2: Verifica compatibilità SyncroV2
      console.log('📋 Test 2: Verifica compatibilità SyncroV2...');
      result.syncroV2Compatible = await this.testSyncroV2Compatibility();

      if (!result.syncroV2Compatible) {
        result.errors.push('Sistema SyncroV2 non compatibile');
      }

      // Test 3: Verifica integrazione store NgRx
      console.log('📋 Test 3: Verifica integrazione store NgRx...');
      result.storeIntegration = await this.testStoreIntegration();

      if (!result.storeIntegration) {
        result.warnings.push('Integrazione store NgRx potrebbe avere problemi');
      }

      // Test 4: Test operazioni CRUD unificate
      console.log('📋 Test 4: Test operazioni CRUD unificate...');
      await this.testUnifiedCRUDOperations();

      console.log('✅ Test completo del sistema unificato completato');

    } catch (error) {
      result.errors.push(`Errore durante i test: ${error.message}`);
      console.error('❌ Errore durante i test del sistema unificato:', error);
    }

    return result;
  }

  /**
   * Test compatibilità con SyncroV2
   */
  private async testSyncroV2Compatibility(): Promise<boolean> {
    try {
      // Test aggiornamento schema database
      await this.syncroV2DbService.updateDatabaseSchema();

      // Test verifica colonne v2
      const testCategories = await this.hybridDbService.getAll(['categories'], ['id', 'lastSyncTimestamp', 'syncedWithV2']);

      // Se non ci sono errori, il sistema è compatibile
      return true;
    } catch (error) {
      console.error('❌ Test compatibilità SyncroV2 fallito:', error);
      return false;
    }
  }

  /**
   * Test integrazione store NgRx
   */
  private async testStoreIntegration(): Promise<boolean> {
    try {
      // Verifica che lo store sia disponibile
      if (!this.store) {
        console.warn('⚠️ Store NgRx non disponibile');
        return false;
      }

      // Test dispatch di una action di test (non modifica dati reali)
      // Questo è solo per verificare che il meccanismo funzioni
      return true;
    } catch (error) {
      console.error('❌ Test integrazione store fallito:', error);
      return false;
    }
  }

  /**
   * Test operazioni CRUD unificate
   */
  private async testUnifiedCRUDOperations(): Promise<void> {
    const testTableName = 'test_unified_crud';
    const testData = {
      id: 'test_unified_' + Date.now(),
      name: 'Test Unified CRUD',
      timestamp: Date.now().toString()
    };

    try {
      console.log('🔧 Test CREATE...');
      await this.hybridDbService.addRecord(
        testTableName,
        ['id', 'name', 'timestamp'],
        [testData.id, testData.name, testData.timestamp]
      );

      console.log('🔍 Test READ...');
      const readResult = await this.hybridDbService.getRecordsByANDCondition(
        testTableName,
        [{ key: 'id', value: testData.id }]
      );

      if (readResult.length === 0) {
        throw new Error('Record non trovato dopo l\'inserimento');
      }

      console.log('✏️ Test UPDATE...');
      await this.hybridDbService.updateRecord(
        testTableName,
        [{ key: 'id', value: testData.id }],
        [{ key: 'name', value: 'Test Updated' }]
      );

      console.log('🗑️ Test DELETE...');
      await this.hybridDbService.deleteRecord(
        testTableName,
        [{ key: 'id', value: testData.id }]
      );

      console.log('✅ Test CRUD unificato completato con successo');
    } catch (error) {
      console.error('❌ Test CRUD unificato fallito:', error);
      throw error;
    }
  }

  /**
   * Mostra report completo del sistema unificato
   */
  async showUnifiedSystemReport(): Promise<string> {
    const testResult = await this.testUnifiedDatabaseSystem();

    let report = `📊 REPORT SISTEMA UNIFICATO DATABASE\n`;
    report += `===========================================\n`;
    report += `Platform: ${testResult.platform}\n`;
    report += `Database Funzionante: ${testResult.databaseWorking ? '✅' : '❌'}\n`;
    report += `SyncroV2 Compatibile: ${testResult.syncroV2Compatible ? '✅' : '❌'}\n`;
    report += `Store NgRx Integrato: ${testResult.storeIntegration ? '✅' : '⚠️'}\n`;

    if (testResult.errors.length > 0) {
      report += `\n❌ ERRORI:\n`;
      testResult.errors.forEach(error => {
        report += `- ${error}\n`;
      });
    }

    if (testResult.warnings.length > 0) {
      report += `\n⚠️ AVVERTIMENTI:\n`;
      testResult.warnings.forEach(warning => {
        report += `- ${warning}\n`;
      });
    }

    const allGood = testResult.databaseWorking && testResult.syncroV2Compatible && testResult.storeIntegration;

    if (allGood) {
      report += `\n🎉 SISTEMA UNIFICATO COMPLETAMENTE FUNZIONANTE!\n`;
      report += `Il database è pronto per la sincronizzazione v2 con aggiornamento automatico degli store NgRx.`;
    } else {
      report += `\n⚠️ SISTEMA PARZIALMENTE FUNZIONANTE\n`;
      report += `Alcuni componenti potrebbero non funzionare correttamente.`;
    }

    console.log(report);
    return report;
  }
}
