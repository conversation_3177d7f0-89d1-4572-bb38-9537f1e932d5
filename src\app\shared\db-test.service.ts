import { Injectable } from '@angular/core';
import { Platform } from '@ionic/angular';
import { IndexedDbService } from './indexeddb.service';
import { HybridDbService } from './hybrid-db.service';

@Injectable({
  providedIn: 'root'
})
export class DbTestService {

  constructor(
    private platform: Platform,
    private indexedDbService: IndexedDbService,
    private hybridDbService: HybridDbService
  ) {}

  /**
   * Testa la disponibilità e funzionalità del database
   */
  async testDatabaseAvailability(): Promise<{
    platform: string;
    indexedDbSupported: boolean;
    indexedDbAvailable: boolean;
    hybridDbWorking: boolean;
    errors: string[];
  }> {
    const result = {
      platform: this.platform.is('capacitor') ? 'mobile' : 'web',
      indexedDbSupported: false,
      indexedDbAvailable: false,
      hybridDbWorking: false,
      errors: [] as string[]
    };

    try {
      // Test 1: Verifica supporto IndexedDB
      if (typeof window !== 'undefined' && window.indexedDB) {
        result.indexedDbSupported = true;
      } else {
        result.errors.push('IndexedDB non è supportato in questo browser');
      }

      // Test 2: Verifica disponibilità IndexedDB (solo su web)
      if (result.platform === 'web' && result.indexedDbSupported) {
        try {
          result.indexedDbAvailable = await this.indexedDbService.isAvailable();
          if (result.indexedDbAvailable) {
          } else {
            result.errors.push('IndexedDB non è disponibile (potrebbe essere modalità privata o storage pieno)');
          }
        } catch (error) {
          result.errors.push(`Errore nel test IndexedDB: ${error.message}`);
          console.error('❌ Errore nel test IndexedDB:', error);
        }
      }

      // Test 3: Verifica funzionalità HybridDbService
      try {
        // Test di lettura (dovrebbe funzionare anche se le tabelle non esistono)
        const testData = await this.hybridDbService.getAll(['categories'], ['id']);
        result.hybridDbWorking = true;
      } catch (error) {
        result.errors.push(`Errore nel test HybridDbService: ${error.message}`);
        console.error('❌ Errore nel test HybridDbService:', error);
      }

      // Test 4: Verifica esistenza tabelle principali
      if (result.hybridDbWorking) {
        try {
          await this.testTableExistence();
        } catch (error) {
          result.errors.push(`Errore nel test tabelle: ${error.message}`);
          console.warn('⚠️ Test tabelle fallito:', error);
        }
      }

      // Test 5: Test di scrittura (solo se tutto funziona)
      if (result.hybridDbWorking) {
        try {
          await this.testWriteOperation();
        } catch (error) {
          result.errors.push(`Errore nel test di scrittura: ${error.message}`);
          console.warn('⚠️ Test di scrittura fallito:', error);
        }
      }

    } catch (error) {
      result.errors.push(`Errore generale nel test database: ${error.message}`);
      console.error('❌ Errore generale nel test database:', error);
    }

    return result;
  }

  /**
   * Test per verificare l'esistenza delle tabelle principali
   */
  private async testTableExistence(): Promise<void> {
    const requiredTables = ['customers', 'categories', 'products', 'userSettings'];

    for (const tableName of requiredTables) {
      try {
        const records = await this.hybridDbService.getAll([tableName], ['id']);
      } catch (error) {
        console.warn(`⚠️ Problema con tabella ${tableName}:`, error);
        throw new Error(`Tabella ${tableName} non accessibile: ${error.message}`);
      }
    }
  }

  /**
   * Test di scrittura per verificare che il database sia scrivibile
   */
  private async testWriteOperation(): Promise<void> {
    const testTableName = 'userSettings'; // Usa una tabella esistente
    const testData = {
      id: 'test_' + Date.now(),
      username: 'test_user',
      type: 'test_type',
      value: 'test_value',
      instant: Date.now().toString()
    };

    try {
      // Prova a scrivere un record di test
      await this.hybridDbService.addRecord(
        testTableName,
        ['id', 'username', 'type', 'value', 'instant'],
        [testData.id, testData.username, testData.type, testData.value, testData.instant]
      );

      // Prova a leggere il record
      const records = await this.hybridDbService.getAll([testTableName]);
      const foundRecord = records.find(r => r.id === testData.id);

      if (foundRecord) {

        // Pulisci il record di test
        try {
          await this.hybridDbService.deleteRecord(testTableName, [{ key: 'id', value: testData.id }]);
        } catch (cleanupError) {
          console.warn('⚠️ Errore nella pulizia del record di test:', cleanupError);
        }
      } else {
        throw new Error('Record di test non trovato dopo la scrittura');
      }

    } catch (error) {
      throw new Error(`Test di scrittura fallito: ${error.message}`);
    }
  }

  /**
   * Mostra un report dettagliato dello stato del database
   */
  async showDatabaseReport(): Promise<string> {
    const testResult = await this.testDatabaseAvailability();
    
    let report = `📊 REPORT DATABASE\n`;
    report += `Platform: ${testResult.platform}\n`;
    report += `IndexedDB Supportato: ${testResult.indexedDbSupported ? '✅' : '❌'}\n`;
    report += `IndexedDB Disponibile: ${testResult.indexedDbAvailable ? '✅' : '❌'}\n`;
    report += `HybridDB Funzionante: ${testResult.hybridDbWorking ? '✅' : '❌'}\n`;
    
    if (testResult.errors.length > 0) {
      report += `\n⚠️ ERRORI:\n`;
      testResult.errors.forEach(error => {
        report += `- ${error}\n`;
      });
    }

    if (testResult.hybridDbWorking) {
      report += `\n✅ Il database è pronto per la sincronizzazione`;
    } else {
      report += `\n❌ Il database ha problemi, la sincronizzazione potrebbe non funzionare correttamente`;
    }

    console.log(report);
    return report;
  }
}
