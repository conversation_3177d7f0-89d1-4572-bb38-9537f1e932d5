import { CommonModule } from '@angular/common';
import { Component, EventEmitter, inject, OnInit, Output } from '@angular/core';
import { IonicModule } from '@ionic/angular';
import { Store, select } from '@ngrx/store';
import { TranslateModule } from '@ngx-translate/core';
import { Observable } from 'rxjs';
import { NavigationItem } from 'src/app/service/data/navigation-item';
import { Selection } from 'src/app/shared/selection';
import { editNavigationTree, setNavigationTree } from 'src/app/store/actions/navigation-tree.actions';
import { addIcons } from 'ionicons';
import { closeOutline } from 'ionicons/icons';
import { CatalogService } from 'src/app/service/catalog/catalog.service';
import Navigations from 'src/app/shared/navigations';
import { Router } from '@angular/router';

@Component({
  selector: 'app-navigation-panel',
  templateUrl: './navigation-panel.component.html',
  styleUrls: ['./navigation-panel.component.scss'],
  standalone: true,
  imports: [
    IonicModule,
    TranslateModule,
    CommonModule
  ]
})
export class NavigationPanelComponent implements OnInit {
  @Output() navToPage = new EventEmitter();
  @Output() hideNavigator = new EventEmitter();
  navigationItems: NavigationItem[] = [];
  navigationQueue$: Observable<{ item: Selection }[]> = this._store.pipe(select('navStack'))
  queueToOpen = [];
  catalogService = inject(CatalogService);
  private _router = inject(Router);

  constructor(private _store: Store<any>) {
    addIcons({ closeOutline });
  }

  ngOnInit(): void {
    // Sottoscrizione al navigationTree del store
    this._store.pipe(select('navigationTree')).subscribe((navigationTree) => {
      if (navigationTree && navigationTree.length > 0) {
        this.navigationItems = navigationTree;
      }
    });

    this.catalogService.catalogsAreLoaded.subscribe((loaded) => {
      this._store.pipe(select('categories')).subscribe((catalog) => {
        if (catalog && catalog.length > 0) {


          const hasChildren = (itemId: number): boolean => {
            return catalog.some(child => Number(child.idParent) === itemId && child.isProduct !== true);
          };


          const navigationItems = catalog
            .filter(item => {
              return item.isProduct != true && item.idApp != 'it';
            })
            .map(item => {
              const isRoot = item.idRootCategory === item.idSubCategory;
              const navigationItem = {
                level: item.level,
                id: item.id.toString(),
                categoryName: item.name,
                expandible: hasChildren(item.id), // calcolo se ha figli
                parent: isRoot ? null : (item.idParent ? item.idParent.toString() : null),
                expanded: false
              } as NavigationItem;
              return navigationItem;
            });

          // Inizializza il store se non è già stato fatto
          this._store.dispatch(setNavigationTree({ items: navigationItems }));
        }

      });

    });
  }

  hide() {
    this.hideNavigator.emit();
  }

  parentIsExpanded(items: NavigationItem[], item: NavigationItem) {
    const found = items.filter(array => array.id === item.parent);
    const result = !!found && found.length > 0 && found[0].expanded ? found[0].expanded : false;
    return result;
  }

  sonIsExpanded(items: NavigationItem[], item: NavigationItem) {
    const found = items.filter(array => array.parent === item.id);
    return !!found && found.length > 0 && found[0].expanded ? found[0].expanded : false;
  }

  expandCollapse(item: NavigationItem, forceClose = false) {
    console.log('ExpandCollapse called for item:', item, 'forceClose:', forceClose);
    this.queueToOpen = [];
    let newItem = Object.assign({}, item);
    newItem.expanded = forceClose ? false : !newItem.expanded;
    console.log('New item state:', newItem);
    this._store.dispatch(editNavigationTree({ item: newItem }));

    if (!newItem.expanded) {
      // Trova e chiudi tutti i figli
      const children = this.navigationItems.filter(array => array.parent === item.id);
      console.log('Closing children:', children);
      children.forEach(child => {
        this.expandCollapse(child, true);
      });
    }
  }

  private setExpandedForChild(navigationItems: NavigationItem[], item: NavigationItem) {
    let newNavigationItems = navigationItems.map(element => {
      let newItem = Object.assign({}, element);
      newItem.expanded = false;
      return newItem;
    });
    if (this.queueToOpen.length > 0) {
      this.queueToOpen.forEach((item) => {
        newNavigationItems = newNavigationItems.map((element) => {
          let newItem = Object.assign({}, element);
          if (newItem.id === item)
            newItem.expanded = true;
          return newItem;
        });
      });
    } else {
      let navStack = [];
      this._store.pipe(select('navStack')).subscribe(res => navStack = res).unsubscribe();
      if (!!navStack && navStack.length > 0) {
        navStack = navStack.map((roba) => (roba.item as Selection).id);
        const chain = this.findChainById(navigationItems, navStack[navStack.length - 1]);
        chain.forEach((item) => {
          newNavigationItems = newNavigationItems.map((element) => {
            let newItem = Object.assign({}, element);
            if (newItem.id === item.id)
              newItem.expanded = true;
            return newItem;
          });
        });
      }
    }
    this._store.dispatch(setNavigationTree({ items: newNavigationItems }));
  }

  private findChainById(navigationItems: NavigationItem[], itemid: string) {
    var chain: NavigationItem[] = [];

    const findObject = (id: string) => {
      const object = navigationItems.find(obj => obj.id === id);
      if (object) {
        chain.push(object);
        if (object.parent !== null) {
          findObject(object.parent);
        }
      }
    };

    findObject(itemid);
    return chain;
  }

  navToCategory(item: NavigationItem) {
    console.log('Navigating to category:', item);
    this._router.navigate(['/private/catalog'], {
      queryParams: {
        search: item.id,
      }
    });

    this.hide();
  }

  private findParent(parentId) {
    const parent = this.navigationItems.find(item => item.id === parentId);
    if (!!parent && !!parent.parent) {
      this.queueToOpen.push(parent.parent);
      this.findParent(parent.parent);
    }
  }
}
