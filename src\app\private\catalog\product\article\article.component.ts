import { ChangeDetectorRef, Component, EventEmitter, Input, OnInit, Output, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { IonicModule, Platform, PopoverController } from '@ionic/angular';
import { Classification, Image, Product } from 'src/app/service/data/product';
import Utils from 'src/app/shared/utils';
import { DocumentService } from 'src/app/service/document/document.service';
import { BehaviorSubject, Observable } from 'rxjs';
import { ProductInCart } from 'src/app/service/data/product-in-cart';
import { HybridDbService } from 'src/app/shared/hybrid-db.service';
import { LocalStorageService } from 'src/app/service/local-storage.service';
import { Cart } from 'src/app/service/data/cart';
import { setCart } from 'src/app/store/actions/cart.actions';
import { Store } from '@ngrx/store';
import { QuantityPopoverComponent } from 'src/app/private/quantity-popover/quantity-popover.component';
import { Keyboard } from '@capacitor/keyboard';
import { Share } from '@capacitor/share';
import { Network } from '@capacitor/network';
import { SafePipe } from 'src/app/shared/safe-pipe';
import { ZeroRemoverPipe } from 'src/app/shared/zero-remover.pipe';
import { CommonModule } from '@angular/common';
import { ProductQuantityPipe } from 'src/app/shared/product-quantity';
import { addIcons } from 'ionicons';
import { closeOutline } from 'ionicons/icons';
  
@Component({
    selector: 'app-article',
    templateUrl: './article.component.html',
    styleUrls: ['./article.component.scss'],
    standalone: true,
    schemas: [CUSTOM_ELEMENTS_SCHEMA],
    imports: [
      IonicModule,
      SafePipe,
      ZeroRemoverPipe,
      CommonModule,
      ProductQuantityPipe
    ]
})
export class ArticleComponent implements OnInit {
  @Input() article: Product;
  @Input() isProspect: boolean;
  @Input() customerUid: string;
  @Output() closePopover = new EventEmitter();
  @Output() addToCart = new EventEmitter();
  @Output() imageViewer = new EventEmitter();
  productInCart$: BehaviorSubject<ProductInCart[]> = new BehaviorSubject<ProductInCart[]>([]);
  cart: BehaviorSubject<Cart> = new BehaviorSubject<Cart>(null);
  datacolCategoryImage: Image[] = [];;
  resolvedArticleImage: string = null;
  resolvedShortCaption: string | null = null;
  imageDirectory: string = localStorage.getItem('imageDirectory');
  appIsOnline$: Observable<boolean> = undefined;
  loadOtherImages = false;

  constructor(private _platform: Platform,  
    private _service: DocumentService,
    private _dbService: HybridDbService, private _lsService: LocalStorageService,
    private _cdref: ChangeDetectorRef, private _store: Store<any>,
    private popoverController: PopoverController) { 
      addIcons({ closeOutline });
    }

  async ngOnInit() {
    await this._platform.ready();

    if (this._platform.is('capacitor')) {
      Keyboard.addListener('keyboardWillShow', (info) => {
        this.adjustPopoverPosition(info.keyboardHeight);
      });

      Keyboard.addListener('keyboardWillHide', () => {
        this.resetPopoverPosition();
      });
      
      const resolved = await Utils.resolveImageUrl(this._platform, this.imageDirectory, this.article.image)
      this.resolvedArticleImage = !!resolved ? resolved : null;
      if (this._platform.is('capacitor')) {
        Network.getStatus().then(status => {
          this.loadOtherImages = status.connected;
        });
      } else
        this.loadOtherImages = true;
      const images = Utils.objToJson(this.article.images);
      if (!!images) {
        const principal: Image[] | null = Utils.objToJson(this.article.images).filter((image: Image) => image.imageType === 'PRIMARY')
        this.resolvedShortCaption = !!principal && principal.length > 0 ? principal[0].shortCaption : '';

        if (this.loadOtherImages) {
          this.datacolCategoryImage = this.obtainFilteredImage(Utils.objToJson(this.article.images));
        }
      }
    }
    Utils.checkInternet().then(isOnline => {
      this.loadOtherImages = isOnline;
      this._cdref.detectChanges();
    });
    await this.loadCart();
  }

  adjustPopoverPosition(keyboardHeight: number) {
    const popoverElement = document.querySelector('.quantity-popover');
    if (popoverElement) {
      popoverElement.setAttribute('style', `--offset-y: -${keyboardHeight / 2}px !important;`);
    }
  }

  resetPopoverPosition() {
    const popoverElement = document.querySelector('.quantity-popover');
    if (popoverElement) {
      popoverElement.setAttribute('style', '');
    }
  }

  private async loadCart() {
    if (!!this.customerUid && !this.isProspect) {
      await this._dbService.getRecordsByANDCondition('carts', [{ key: 'agentCode', value: this._lsService.get("customerAgentCode") }, { key: 'idCustomer', value: this.customerUid }]).then(async (data: Cart[]) => {
        if (!!data && data.length > 0 && !!data[0]) {
          this.cart.next({ ...data[0] });
          if (!!data[0].products)
            this.productInCart$.next(Utils.objToJson(data[0].products));
        } else {
          this.productInCart$.next([]);
        }
      });
    }
  }

  private obtainFilteredImage(images: Image[]) {
    if (!!images) {
      images = images.filter(image => image.imageType === 'GALLERY');
      if (images.length > 0) {
        const shorter = images.map(item => {
          const short = item.url.replace('https://web.datacol.com/imghyb/', '').substring(2); // può esserci 'Q/', 'L/', 'B/' etc.
          const size = short.substring(0, short.indexOf('W'));
          return {
            size: size, name: item.url.substring(item.url.lastIndexOf('/') + 1), url: item.url, shortCaption: item.shortCaption,
            mediaTypology: item.mediaTypology, positionMc: item.positionMc
          }
        });
        const groupped = shorter.reduce((hash: any, obj: any) => ({ ...hash, [obj['name']]: (hash[obj['name']] || []).concat(obj) }), {});

        Object.keys(groupped).forEach(function (k) {
          groupped[k] = groupped[k].sort((a: any, b: any) => parseInt(b.size) - parseInt(a.size));
        });

        let secondaryImages = [];
        Object.keys(groupped).forEach(function (k) {
          secondaryImages.push(groupped[k][0]);
        });
        secondaryImages = secondaryImages.sort((a: any, b: any) => parseInt(a.mediaTypology.substring(0, 2)) - parseInt(b.mediaTypology.substring(0, 2)) ||
          parseInt(a.positionMc) - parseInt(b.positionMc));
        return secondaryImages;
      }
    }
    return [];
  }

  getClassifications(): Classification[] {
    return (Utils.objToJson(this.article.classifications) as Classification[]);
  }

  close() {
    this.closePopover.emit();
  }

  async showMultipleQuantity() {
    if (document.querySelector('.quantity-popover') == null) {
      const popover = await this.popoverController.create({
        component: QuantityPopoverComponent,
        event: event,
        cssClass: 'quantity-popover',
        translucent: true,
        componentProps: { product: this.article }
      });
      popover.onDidDismiss().then((data) => {
        if (!!data.data && !!data.data.newQuantity)
          this.addOneToCart(+data.data.newQuantity);
      });

      return await popover.present();
    }
  }

  async addOneToCart(multiQuantity?: number) {
    if (!this.isProspect) {

      // this.addToCart.emit(this.article);
      var cart: Cart = Object.assign({}, this.cart.getValue() as Cart);
      if (!!cart && !!cart.products) {
        cart.products = Utils.objToJson(cart.products);
        const index = cart.products.findIndex(item => item.idProduct === this.article.code);
        if (index < 0) {
          cart.products.push({ idProduct: this.article.code, quantity: (multiQuantity ? multiQuantity : +this.article.minimumDeliveryQuantity), description: this.article.name, image: this.article.image, minimumDeliveryQuantity: +this.article.minimumDeliveryQuantity });
        } else {
          cart.products[index].quantity = multiQuantity ? multiQuantity : (cart.products[index].quantity + +this.article.minimumDeliveryQuantity);
        }
        await this._dbService.updateRecord('carts', [{ key: 'id', value: cart.id }], [{ key: 'products', value: JSON.stringify(cart.products) }]).then(_ => {
          this._store.dispatch(setCart({ cart: { idCustomer: this.customerUid, idCart: +cart.id, currentQuantityInCart: (cart.products.length) } }));
          this.cart.next({ ...cart });
        });
      } else {
        var productsInCart = [{ idProduct: this.article.code, quantity: (multiQuantity ? multiQuantity : +this.article.minimumDeliveryQuantity), description: this.article.name, image: this.article.image, minimumDeliveryQuantity: this.article.minimumDeliveryQuantity }];
        await this._dbService.insertOrReplace("carts", [{ key: 'agentCode', value: this._lsService.get("customerAgentCode") }, { key: 'idCustomer', value: this.customerUid }],
          ['id', 'agentCode', 'idCustomer', 'products'],
          [this._lsService.get("customerAgentCode"), this.customerUid, JSON.stringify(productsInCart)]).then(async _ => {
            var cartId = -1;
            await this._dbService.getRecordsByANDCondition('carts', [{ key: 'agentCode', value: this._lsService.get("customerAgentCode") }, { key: 'idCustomer', value: this.customerUid }]).then(async (data: Cart[]) => {
              if (!!data[0] && !!data[0]['id']) {
                this.cart.next({ ...data[0] });
                const rows = data[0];
                cartId = rows.id;
              }
            });
            if (cartId > 0)
              this._store.dispatch(setCart({ cart: { idCustomer: this.customerUid, idCart: cartId, currentQuantityInCart: (productsInCart.length) } }));
          }
          );
      }
      this.addToCart.emit();
    }
  }

  async share(event) {
    event.stopPropagation();
    // es: https://eshop.datacol.com/store/datacol/it/EUR/p/YAC100030
    const url = this._service.getSharableUrl(this.article.code, 'p');
    await Share.share({
      title: this.article.name,
      text: this.article.name,
      url
    });
  }

  openImageViewer(imageUrl) {
    this.imageViewer.emit(imageUrl);
  }
}
