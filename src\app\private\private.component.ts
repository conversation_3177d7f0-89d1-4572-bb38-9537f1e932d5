import { AfterViewInit, ChangeDetectorRef, Component, EventEmitter, inject, OnInit, Output, ViewChild } from '@angular/core';
import { Router, RouterModule } from '@angular/router';
import { SplashScreen } from '@capacitor/splash-screen';
import { AlertController, IonButton, IonButtons, IonContent, IonFooter, IonHeader, IonTitle, IonToolbar, Platform, ToastController } from '@ionic/angular/standalone';
import { select, Store } from '@ngrx/store';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { Subscription } from 'rxjs';
import { CategoryService } from '../service/category/category.service';
import { Category } from '../service/data/category';
import { Favorite } from '../service/data/favorite';
import { Subcategory } from '../service/data/subcategory';
import { JWTTokenService } from '../service/jwttoken.service';
import { HybridDbService } from '../shared/hybrid-db.service';
import Navigations from '../shared/navigations';
import Utils from '../shared/utils';
import { setCatalogByProduct } from '../store/actions/catalog-by-product.actions';
import { setFavorites } from '../store/actions/favorites.actions';
import { setDCProperty } from '../store/actions/dcproperty.actions';
import { setSubcategories } from '../store/actions/subcategories.actions';
import { CartComponent } from './carts/cart/cart.component';
import { CartsComponent } from './carts/carts.component';
import { CatalogComponent } from './catalog/catalog.component';
import { ProductComponent } from './catalog/product/product.component';
import { CustomersComponent } from './customers/customers.component';
import { ExtrasComponent } from './extras/extras.component';
import { SettingsComponent } from './settings/settings.component';
import { SyncroComponent } from './syncro/syncro.component';
import { Capacitor } from '@capacitor/core';
import { App } from '@capacitor/app';
import { ProductsCarouselComponent } from './catalog/products-carousel/products-carousel.component';
import { Product } from '../service/data/product';
import { setProducts } from '../store/actions/products.actions';
import { Directory, Filesystem, GetUriOptions } from '@capacitor/filesystem';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ToolbarComponent } from './toolbar/toolbar.component';
import { CatalogService } from '../service/catalog/catalog.service';
import { NavigationService } from '../service/navigation/navigation.service';
@Component({
    selector: 'app-private',
    templateUrl: './private.component.html',
    styleUrls: ['./private.component.scss'],
    standalone: true,
    imports: [
      CommonModule,
      FormsModule,
      ReactiveFormsModule,
      TranslateModule,
      ToolbarComponent,
      RouterModule,
      IonToolbar,
      IonHeader,
      IonContent,
      IonFooter,
      IonTitle,
      IonButton,
      IonButtons
    ]
})
export class PrivateComponent implements OnInit, AfterViewInit {

  catalogService = inject(CatalogService);
  navigationService = inject(NavigationService);
  @Output() goComponentGoBack = new EventEmitter();
  @ViewChild('navigationQueue') navigationQueue;
  count = 0;
  subscription: Subscription;
  catalogCmp : CatalogComponent = null;
  productCmp : ProductComponent = null;
  productCarouselCmp : ProductsCarouselComponent = null;
  customersCmp : CustomersComponent = null;
  cartCmp: CartComponent = null;
  cartsCmp: CartsComponent = null;
  extrasCmp: ExtrasComponent = null;
  settingsCmp: SettingsComponent = null;
  syncroCmp: SyncroComponent = null;
  cartsCounter$ = this._store.pipe(select('cart'));
  private subjects: Subscription;
  utilityMenuShowed: boolean = false;

  constructor(private _cdref: ChangeDetectorRef, private _router: Router,
    private _platform: Platform,
    private _jwtService: JWTTokenService,
    private _alertController: AlertController, private _translate: TranslateService, private _toastController: ToastController,
    private _dbService: HybridDbService, private _store: Store<any>, private _categoryService: CategoryService) {

      _platform.ready().then(() => {
        this._platform.resume.subscribe(() => {
          if (this._jwtService.getIss() === 'datacol') {
            if (this._jwtService.isTokenExpired()) {
              this._router.navigate(['/login'], { queryParams: { previusUrl: this._router.url}});
            }
          } else {
            this._router.navigate(['/login'], { queryParams: { previusUrl: this._router.url}});
          }
        });
      });

      _platform.backButton.subscribeWithPriority(10, async () => {
        const alert = await this._alertController.create({
          header: this._translate.instant('GENERICS.ATTENTION'),
          message: this._translate.instant('GENERICS.GO_OUT'),
          buttons: [
            {
              text: this._translate.instant('HOME.CANCEL'),
              role: 'cancel',
              cssClass: 'secondary',
              id: 'cancel-button',
              handler: (blah) => {
              }
            }, {
              text: this._translate.instant('HOME.CONFIRM'),
              id: 'confirm-button',
              handler: async () => {
                App.exitApp();
              }
            }
          ]
        });
        await alert.present();
      });

      if(!!localStorage.getItem('language'))
      {
        this._translate.use(localStorage.getItem('language'));
      }
  }


  async ngOnInit() {
    await this._platform.ready();
    if (this._platform.is('capacitor')) {
      try {
        const options: GetUriOptions = {
          directory: Directory.Data,
          path: 'imageDirectory'
        };
        const directoryUri = await Filesystem.getUri(options);
        localStorage.setItem("imageDirectory", Capacitor.convertFileSrc(directoryUri.uri));
      } catch (error) {
        console.error('Error accessing filesystem:', error);
      }
    }
  }

  ngOnDestroy() {
    if(!!this.subjects)
      this.subjects.unsubscribe();
  }

  async ngAfterViewInit() {
    await SplashScreen.hide();
    // Preparo la navigazione piatta
    setTimeout(async () => {
      let catalog = [];
      let dcProperty = [];
      let favorites = [];
      let subcategories = [];
      let products = [];
      this._store.pipe(select('catalogByProduct')).subscribe(res => catalog = Array.isArray(res) ? [...res] : []).unsubscribe();
      if(catalog.length === 0)
      {
        await this._dbService.getAll(["categories"], ['*']).then(async (data) => {
          catalog = data;
          let items: Array<{rootId: string, items: Category[]}> = [];
          for (let i = 0; i < data.length; i++) {
            const element = data[i];
            const previewsItem = await this._categoryService.catalogByProduct(Utils.objToJson(element.subcategories));
            items.push({rootId: element.id, items: previewsItem});
          }
          if(items.length > 0)
            this._store.dispatch(setCatalogByProduct({items}));
        });
      }
      this._store.pipe(select('subcategories')).subscribe(res => subcategories = Array.isArray(res) ? [...res] : []).unsubscribe();
      if(subcategories.length === 0)
      {
        await this._dbService.getAll(["subcategories"], ['*']).then((items: Subcategory[]) => {
          this._store.dispatch(setSubcategories({items}));
        });
      }
      this._store.pipe(select('dcproperty')).subscribe(res => dcProperty = Array.isArray(res) ? [...res] : []).unsubscribe();
      if(dcProperty.length === 0)
      {
        await this._dbService.getAll(["datacolCategories"], ['*']).then(async (data) => {
          let items: Array<{idSubCategory: string, focus: string, divisionStatusCode: string}> = [];
          for (let i = 0; i < data.length; i++) {
            const element = data[i];
            items.push({idSubCategory: element.id, focus: element.focus, divisionStatusCode: element.divisionStatusCode});
          }
          if(items.length > 0)
            this._store.dispatch(setDCProperty({items}));
        });
      }
      this._store.pipe(select('favorites')).subscribe(res => favorites = [...res]).unsubscribe();
      if(favorites.length === 0)
      {
        await this._dbService.getAll(["favorites"], ['*']).then((data: Favorite[]) => {
          this._store.dispatch(setFavorites({items: data}));
        });
      }
      this._store.pipe(select('products')).subscribe(res => products = [...res]).unsubscribe();
      if(products.length === 0)
      {
        await this._dbService.getAll(['products'], ['idSubCategory', 'code', 'image', 'name', 'minimumDeliveryQuantity']).then(async (data: Product[]) => {
          const items = await Promise.all((data as Product[]).map(async (item:Product) => {
            const imageDirectory: string = localStorage.getItem("imageDirectory");
            const resolvedImage = await Utils.resolveImageUrl(this._platform, imageDirectory, item.image);
            return { idSubCategory: item.idSubCategory ,code: item.code, image: resolvedImage, name: item.name, minimumDeliveryQuantity: item.minimumDeliveryQuantity };
          }));
          const distinctItems = items.filter((value, index, self) =>
            index === self.findIndex((t) => (
                t.code === value.code
            ))
          );
  
          this._store.dispatch(setProducts({ items: distinctItems }));
        });
      }

      // Setto la navigazione
      let navigations = new Navigations(this._store);
      navigations.setNavigationTree(catalog);
      navigations = undefined;
    }, 1000);
  }

  ngAfterContentChecked() {
    this._cdref.detectChanges();
  }

  showUtilityMenu(event) {
    event.stopPropagation();
    this.utilityMenuShowed = true
  }

  async logout(){
    const alert = await this._alertController.create({
      header: this._translate.instant('HOME.CONF_TITLE'),
      message: this._translate.instant('HOME.CONF_MSG'),
      buttons: [
        {
          text: this._translate.instant('HOME.CANCEL'),
          role: 'cancel',
          cssClass: 'secondary',
          id: 'cancel-button',
          handler: (blah) => {
          }
        }, {
          text: this._translate.instant('HOME.CONFIRM'),
          id: 'confirm-button',
          handler: async () => {
            this._jwtService.setToken(null);
            this._router.navigate(['/login']);
          }
        }
      ]
    });
    await alert.present();
  }

  gotToCartDetail(event) {
    event.stopPropagation();
    let currentCustomer = null;
    this._store.pipe(select('currentCustomer')).subscribe(res => currentCustomer = res).unsubscribe();
    let cart = {idCustomer: null, idCart: null, currentQuantityInCart: 0};
    this._store.pipe(select('cart')).subscribe(res => cart = res).unsubscribe();
    if(!!cart && !!cart.idCustomer && !!currentCustomer && cart.currentQuantityInCart > 0 && !!cart.idCart)
    {
      this.navigationService.navigateTo('/private/carts/cart');
    } else {
      Utils.showSnack(this._toastController, this._translate.instant("CARTS.EMPTY"), this._translate.instant("SETTINGS.DONE"), true);
    }
  }

  goToHome() {
    this._router.navigate(['/private/home']);
  }

  goBack() {
    if (this._router.url === '/private/carts'){
      this._router.navigate(['/private/home']);
    }
    else if(this._router.url.includes('/private/carts/cart')) {
      this._router.navigate(['/private/carts']);
    } else {
      this._router.navigate(['/private/home']);
    }
  }

  async goTo(path) {
    this.utilityMenuShowed = false;
    if (!!path && path.length > 0) {
      this.navigationService.navigateTo('/private/'+path);
    }
  }

  hasRoute(route: string) {
    return this._router.url.includes(route);
  }

  goCustomGoBack() {
    if(this.catalogCmp !== null) {
      this.catalogCmp.goComponentGoBack(); 
    }
    else if(this.productCmp !== null) {
      this.productCmp.productGoComponentGoBack(); 
    }
    else if(this.cartsCmp !== null) {
      this.cartsCmp.cartsGoComponentGoBack(); 
    }
    else if(this.cartCmp !== null) {
      this.cartCmp.cartGoComponentGoBack(); 
    }
    else if(this.extrasCmp !== null) {
      this.extrasCmp.extraGoComponentGoBack(); 
    }
    else if(this.settingsCmp !== null) {
      this.settingsCmp.settingsGoComponentGoBack(); 
    }
    else if(this.syncroCmp !== null) {
      this.syncroCmp.syncroGoComponentGoBack(); 
    }
    else if(this.productCarouselCmp !== null) {
      this.productCarouselCmp.productGoComponentGoBack(); 
    }
  }

  captureCurrentView() {
    if(this.catalogCmp !== null)
      this.catalogCmp.beforeGoToExtras();
  }

  public onRouterOutletActivate(componentRef : any) {
    this.catalogCmp = (componentRef instanceof CatalogComponent) ? componentRef : null;
    this.productCmp = (componentRef instanceof ProductComponent) ? componentRef : null;
    this.customersCmp = (componentRef instanceof CustomersComponent) ? componentRef : null;
    this.cartCmp = (componentRef instanceof CartComponent) ? componentRef : null;
    this.cartsCmp = (componentRef instanceof CartsComponent) ? componentRef : null;
    this.extrasCmp = (componentRef instanceof ExtrasComponent) ? componentRef : null;
    this.settingsCmp = (componentRef instanceof SettingsComponent) ? componentRef : null;
    this.syncroCmp = (componentRef instanceof SyncroComponent) ? componentRef : null;
    this.productCarouselCmp = (componentRef instanceof ProductsCarouselComponent) ? componentRef : null;
  }

  customerData() {
    let currentCustomer = null;
    this._store.pipe(select('currentCustomer')).subscribe(res => currentCustomer = res).unsubscribe();
    return currentCustomer;
  }

  isProspect(){
    if(this._router.url.includes('/private/catalog'))
    {
      if (this.catalogCmp !== null) 
        return this.catalogService.isProspect;
      else if(this.productCmp !== null) 
        return this.productCmp.isProspect;
      else if (this.productCarouselCmp !== null) 
        return this.productCarouselCmp.isProspect;
      else
        return false;
    }
    else
      return false;
  }

  isFavourites() {
    if(this._router.url.includes('/private/catalog') && this.catalogCmp !== null) {
      return this.catalogCmp.showFavorites;
    }
    else
      return false;
  }

  openSearch() {
    if(this._router.url.includes('/private/catalog') && this.catalogCmp !== null) {
      return this.catalogCmp.openSearch();
    }
    else if(this._router.url.includes('/private/customers') && this.customersCmp !== null) {
      this.customersCmp.openSearch();
    }
    else if(this._router.url.includes('/product') && this.productCmp !== null) {
      this.productCmp.openSearch();
    }
    else if(this._router.url.includes('products-carousel') && this.productCarouselCmp !== null) {
      this.productCarouselCmp.openSearch();
    }
  }
}
